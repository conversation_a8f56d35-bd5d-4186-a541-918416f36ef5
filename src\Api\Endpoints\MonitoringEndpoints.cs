using Api.Extensions;
using Api.Services;

namespace Api.Endpoints;

public static class AdminEndpoints
{
    // ... (existing AdminEndpoints content)

    public static void MapAdminMonitoringEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/monitoring").WithTags("Admin Monitoring");

        // ==================== 系统概览端点 ====================

        group.MapGet("/dashboard", async Task<IResult> (MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.GetDashboardDataAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取监控面板数据").WithDescription("获取完整的监控面板数据，包括系统概览、节点状态、告警摘要等信息");

        group.MapGet("/system/overview", async Task<IResult> (MonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetSystemOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统概览").WithDescription("获取系统整体状态概览，包括节点统计、资源使用率、任务统计等");

        // ==================== 节点监控端点 ====================

        group.MapGet("/nodes/overview", async Task<IResult> (MonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetWorkersOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取所有节点概览").WithDescription("获取所有工作节点的概览信息，包括状态、健康度、资源使用情况");

        group.MapGet("/nodes/{nodeId}", async Task<IResult> (Guid nodeId, WorkerNodeMonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetNodeRealtimeDataAsync(nodeId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取节点详细信息").WithDescription("获取指定节点的详细信息和实时监控数据");

        group.MapGet("/nodes/{nodeId}/metrics", async Task<IResult> (Guid nodeId, MonitoringService monitoringService, int hours = 24) =>
        {
            var result = await monitoringService.GetWorkerMetricsAsync(nodeId, hours);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取节点指标数据").WithDescription("获取指定节点的历史指标数据，可指定时间范围");

        group.MapGet("/nodes/{nodeId}/history",
            async Task<IResult> (Guid nodeId, WorkerNodeMonitoringService monitoringService, DateTime? startTime = null, DateTime? endTime = null) =>
            {
                var result = await monitoringService.GetNodeHistoryDataAsync(nodeId, startTime, endTime);
                return ResultExtensions.ToHttpResult(result);
            }).WithSummary("获取节点历史数据").WithDescription("获取指定节点的历史性能数据，支持自定义时间范围");

        // ==================== 告警管理端点 ====================

        group.MapGet("/alerts", async Task<IResult> (MonitoringDataService monitoringService, int page = 1, int pageSize = 20) =>
        {
            var result = await monitoringService.GetAlertsAsync(page, pageSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取告警列表").WithDescription("分页获取系统告警列表，按创建时间倒序排列");

        group.MapGet("/alerts/summary", async Task<IResult> (MonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetAlertSummaryAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取告警摘要").WithDescription("获取告警统计摘要信息，包括各级别告警数量和最近告警");

        group.MapGet("/nodes/{nodeId}/alerts", async Task<IResult> (Guid nodeId, MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.GetNodeAlertsAsync(nodeId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取节点告警").WithDescription("获取指定节点的所有告警记录");

        group.MapPost("/alerts/{alertId}/resolve", async Task<IResult> (Guid alertId, MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.ResolveAlertAsync(alertId);
            return ResultExtensions.ToHttpResultOrOk(result, "告警已解决");
        }).WithSummary("解决告警").WithDescription("标记指定告警为已解决状态");

        // ==================== 性能分析端点 ====================

        group.MapGet("/performance/summary", async Task<IResult> (MonitoringPerformanceService performanceService) =>
        {
            var result = await performanceService.GetPerformanceSummaryAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取性能摘要").WithDescription("获取系统整体性能摘要，包括资源使用趋势和性能指标");

        group.MapGet("/performance/trends", async Task<IResult> (MonitoringPerformanceService performanceService, int days = 7) =>
        {
            var result = await performanceService.GetPerformanceTrendsAsync(days);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取性能趋势").WithDescription("获取指定天数内的系统性能趋势数据");

        // ==================== 实时监控端点 ====================

        group.MapGet("/realtime/stats", async Task<IResult> (MonitoringRealtimeService realtimeService) =>
        {
            var result = await realtimeService.GetRealtimeStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取实时统计").WithDescription("获取系统实时统计数据，包括当前活跃任务、资源使用等");

        group.MapGet("/health", async Task<IResult> (HealthCheckService healthService) =>
        {
            // TODO: 实现系统健康检查
            await Task.Delay(1);
            var healthStatus = new { Status = "Healthy", Timestamp = DateTime.UtcNow };
            return ResultExtensions.ApiOk(healthStatus);
        }).WithSummary("系统健康检查").WithDescription("检查系统各组件的健康状态，包括数据库、消息队列、工作节点等");

        // ==================== 数据管理端点 ====================

        group.MapPost("/cleanup/old-data", async Task<IResult> (MonitoringDataService monitoringService) =>
        {
            // TODO: 实现清理旧数据逻辑
            await Task.Delay(1);
            return ResultExtensions.ApiOk("旧数据清理完成");
        }).WithSummary("清理旧数据").WithDescription("清理过期的监控数据和告警记录");

        group.MapGet("/statistics", async Task<IResult> (MonitoringDataService monitoringService) =>
        {
            // TODO: 实现统计信息获取逻辑
            await Task.Delay(1);
            var stats = new
            {
                TotalMetricsRecords = 0,
                TotalAlerts = 0,
                DataRetentionDays = 30,
                LastCleanup = DateTime.UtcNow.AddDays(-1)
            };
            return ResultExtensions.ApiOk(stats);
        }).WithSummary("获取监控统计").WithDescription("获取监控系统的统计信息，包括数据量、存储情况等");
    }
}