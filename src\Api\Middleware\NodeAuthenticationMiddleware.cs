using Microsoft.Extensions.Options;

namespace Api.Middleware;

/// <summary>
///     节点认证中间件
/// </summary>
public class NodeAuthenticationMiddleware
{
    private readonly ILogger<NodeAuthenticationMiddleware> _logger;
    private readonly RequestDelegate _next;
    private readonly NodeAuthenticationOptions _options;

    public NodeAuthenticationMiddleware(RequestDelegate next, ILogger<NodeAuthenticationMiddleware> logger, IOptions<NodeAuthenticationOptions> options)
    {
        _next = next;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 检查是否为需要认证的内部API路径
        if (IsInternalApiPath(context.Request.Path))
            if (!await ValidateApiKeyAsync(context))
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized: Invalid API Key");
                return;
            }

        await _next(context);
    }

    /// <summary>
    ///     检查是否为内部API路径
    /// </summary>
    private static bool IsInternalApiPath(PathString path)
    {
        return path.StartsWithSegments("/internal") || path.StartsWithSegments("/api/worker-nodes");
    }

    /// <summary>
    ///     验证API Key
    /// </summary>
    private Task<bool> ValidateApiKeyAsync(HttpContext context)
    {
        try
        {
            // 从Header中获取API Key
            if (!context.Request.Headers.TryGetValue("X-API-Key", out var apiKeyValues))
            {
                _logger.LogWarning("Missing API Key in request to {Path}", context.Request.Path);
                return Task.FromResult(false);
            }

            var apiKey = apiKeyValues.FirstOrDefault();
            if (string.IsNullOrEmpty(apiKey))
            {
                _logger.LogWarning("Empty API Key in request to {Path}", context.Request.Path);
                return Task.FromResult(false);
            }

            // 验证API Key
            if (apiKey != _options.ApiKey)
            {
                _logger.LogWarning("Invalid API Key in request to {Path} from {RemoteIp}", context.Request.Path, context.Connection.RemoteIpAddress);
                return Task.FromResult(false);
            }

            _logger.LogDebug("API Key validation successful for {Path}", context.Request.Path);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating API Key for {Path}", context.Request.Path);
            return Task.FromResult(false);
        }
    }
}

/// <summary>
///     节点认证配置选项
/// </summary>
public class NodeAuthenticationOptions
{
    public const string SectionName = "NodeAuthentication";

    public string ApiKey { get; set; } = "";
}