using Api.Extensions;
using Api.Filters;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AdminEndpoints
{
    public static void MapAdminAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/auth").WithTags("Admin Auth").WithValidation();

        group.MapPost("/login", async Task<IResult> (AdminLoginRequest request, AdminAuthService adminAuthService) =>
        {
            var result = await adminAuthService.LoginAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("管理员登录").WithDescription("使用用户名和密码进行管理员身份验证").AllowAnonymous();

        group.MapPost("/logout", async Task<IResult> (AdminAuthService adminAuthService, HttpContext context) =>
        {
            var result = await adminAuthService.LogoutAsync(context);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("管理员登出").WithDescription("退出管理员登录状态并清除会话");

        group.MapGet("/check", async Task<IResult> (AdminAuthService adminAuthService, HttpContext context) =>
        {
            var result = await adminAuthService.CheckAuthStatusAsync(context);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("验证管理员会话").WithDescription("检查当前管理员登录状态是否有效");

        group.MapPost("/change-password", async Task<IResult> (ChangeAdminPasswordRequest request, AdminAuthService adminAuthService, HttpContext context) =>
        {
            var result = await adminAuthService.ChangePasswordAsync(context, request);
            return ResultExtensions.ToHttpResultOrOk(result, "密码修改成功");
        }).WithSummary("修改管理员密码").WithDescription("更改当前管理员账户的登录密码");
    }

    public static void MapAdminDashboardEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/dashboard").WithTags("Admin Dashboard").WithValidation();

        group.MapGet("/", async Task<IResult> (AdminDashboardService dashboardService) =>
        {
            var result = await dashboardService.GetDashboardDataAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取仪表盘数据").WithDescription("获取管理员仪表盘的完整统计数据和系统概览信息");

        group.MapGet("/health", async Task<IResult> (HealthCheckService healthService) =>
        {
            var result = await healthService.GetSystemHealthAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("系统健康检查").WithDescription("检查数据库、消息队列、工作节点等系统组件的运行状态");

        group.MapGet("/stats/system", async Task<IResult> (SystemAnalyticsService systemAnalyticsService, string timeRange = "realtime",
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await systemAnalyticsService.GetSystemStatsAsync(timeRange, startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统统计").WithDescription("获取系统性能统计数据，支持实时和历史数据查询，时间范围：realtime|1h|24h|7d|custom");

        group.MapGet("/stats/users", async Task<IResult> (UserAnalyticsService userAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await userAnalyticsService.GetUserStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户统计").WithDescription("获取用户注册、活跃度等统计数据，支持按日/周/月分组");

        group.MapGet("/stats/tasks", async Task<IResult> (TaskAnalyticsService taskAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await taskAnalyticsService.GetTaskStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务统计").WithDescription("获取任务执行情况统计数据，支持按时间分组查看趋势");

        group.MapGet("/stats/workers", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点统计").WithDescription("获取工作节点总数、在线数量、健康状态分布和负载统计信息");

        group.MapGet("/stats/proxies", async Task<IResult> (ProxyManagementService proxyService) =>
        {
            var result = await proxyService.GetProxyStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理统计").WithDescription("获取代理池总数、可用代理数量、使用率和健康状态统计");

        group.MapGet("/stats/content", async Task<IResult> (ContentAnalyticsService contentAnalyticsService, string timeRange = "all") =>
        {
            var result = await contentAnalyticsService.GetContentStatsAsync(timeRange);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取内容统计").WithDescription("获取指定时间范围内的内容统计，包括热门视频和黑名单增长趋势");

        group.MapGet("/alerts", async Task<IResult> (MonitoringDataService monitoringService, int page = 1, int pageSize = 20) =>
        {
            var result = await monitoringService.GetAlertsAsync(page, pageSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统告警").WithDescription("分页获取系统告警记录，按时间倒序排列显示");

        group.MapPost("/alerts/{alertId}/acknowledge", async Task<IResult> (Guid alertId, MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.AcknowledgeAlertAsync(alertId);
            return ResultExtensions.ToHttpResultOrOk(result, "告警已确认");
        }).WithSummary("确认告警").WithDescription("将指定告警标记为已确认处理状态");

        group.MapPost("/cleanup", async Task<IResult> (MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.CleanupOldDataAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "旧数据清理完成");
        }).WithSummary("清理过期数据").WithDescription("清理过期的监控数据、告警记录和临时文件");
    }

    public static void MapAdminUserEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/users").WithTags("Admin User Management").WithValidation();

        group.MapGet("/", async Task<IResult> (AdminUserService userService, int page = 1, int pageSize = 20, string? search = null, UserType? userType = null,
            UserAccountStatus? status = null) =>
        {
            var result = await userService.GetUsersAsync(page, pageSize, search, userType, status);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户列表").WithDescription("分页获取用户列表，支持按用户类型、账户状态和关键词进行筛选");

        group.MapGet("/{userId}", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.GetUserDetailAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户详情").WithDescription("获取指定用户的详细信息，包括基本资料、套餐信息和使用统计");

        group.MapPost("/{userId}/disable", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.DisableUserAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户已禁用");
        }).WithSummary("禁用用户").WithDescription("禁用指定用户账户，阻止其登录和使用系统功能");

        group.MapPost("/{userId}/enable", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.EnableUserAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户已启用");
        }).WithSummary("启用用户").WithDescription("启用指定用户账户，恢复其正常的系统访问权限");

        group.MapPost("/{userId}/reset-password", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.ResetUserPasswordAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("重置用户密码").WithDescription("重置指定用户的登录密码，生成新密码并通知用户");

        group.MapPost("/{userId}/modify-plan", async Task<IResult> (Guid userId, UpdateUserPlanRequest request, AdminUserService userService) =>
        {
            var result = await userService.UpdateUserPlanAsync(userId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "用户套餐已修改");
        }).WithSummary("修改用户套餐").WithDescription("修改指定用户的订阅套餐类型和有效期");

        group.MapGet("/{userId}/sessions", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.GetUserSessionsAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户会话").WithDescription("获取指定用户的所有活跃登录会话信息");

        group.MapPost("/{userId}/sessions/revoke", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.RevokeUserSessionsAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户会话已撤销");
        }).WithSummary("撤销用户会话").WithDescription("撤销指定用户的所有登录会话，强制其重新登录");

        group.MapGet("/{userId}/tasks", async Task<IResult> (Guid userId, AdminUserService userService, int page = 1, int pageSize = 20) =>
        {
            var result = await userService.GetUserTasksAsync(userId, page, pageSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户任务").WithDescription("分页获取指定用户的任务历史记录和执行状态");

        group.MapGet("/{userId}/content", async Task<IResult> (Guid userId, AdminUserService userService, int page = 1, int pageSize = 20) =>
        {
            var result = await userService.GetUserContentHistoryAsync(userId, page, pageSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户内容记录").WithDescription("获取指定用户所有任务关联的YouTube内容去重列表");

        group.MapGet("/{userId}/billing", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.GetUserBillingHistoryAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户计费记录").WithDescription("获取指定用户的付费历史记录和当前订阅状态");
    }

    public static void MapAdminTaskEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/tasks").WithTags("Admin Task Management").WithValidation();

        group.MapGet("/", async Task<IResult> (AdminTaskService taskService, int page = 1, int pageSize = 20, WorkerTaskStatus? status = null,
            WorkerTaskType? taskType = null, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await taskService.GetTasksAsync(page, pageSize, status, taskType, startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务列表").WithDescription("分页获取系统任务，支持按执行状态、任务类型和时间范围进行筛选");

        group.MapGet("/{taskId}", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.GetTaskDetailAsync(taskId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务详情").WithDescription("获取指定任务的详细信息，包括执行进度、日志和错误信息");

        group.MapPost("/{taskId}/cancel", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.CancelTaskAsync(taskId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务已取消");
        }).WithSummary("取消任务").WithDescription("强制取消指定任务的执行并更新其状态为已取消");

        group.MapPost("/{taskId}/retry", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.RetryTaskAsync(taskId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务已重试");
        }).WithSummary("重试任务").WithDescription("重新执行失败的任务，重置状态并重新加入执行队列");

        group.MapPost("/{taskId}/delete", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.DeleteTaskAsync(taskId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务记录已删除");
        }).WithSummary("删除任务记录").WithDescription("删除指定任务记录及其关联的文件和日志数据");

        group.MapPost("/cleanup", async Task<IResult> (AdminTaskService taskService, int daysOld = 30) =>
        {
            var result = await taskService.CleanupOldTasksAsync(daysOld);
            return ResultExtensions.ToHttpResultOrOk(result, "任务清理完成");
        }).WithSummary("清理过期任务").WithDescription("批量清理指定天数之前的已完成任务记录和相关文件");
    }

    public static void MapAdminWorkerEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/workers").WithTags("Admin Worker Management").WithValidation();

        group.MapPost("/register", async Task<IResult> (WorkerRegisterRequest request, WorkerService workerService) =>
        {
            var result = await workerService.RegisterWorkerAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("工作节点注册").WithDescription("工作节点向系统注册，提供基础信息和硬件配置").AllowAnonymous();

        group.MapPost("/{workerId}/heartbeat", async Task<IResult> (Guid workerId, WorkerHeartbeat heartbeat, WorkerService workerService) =>
        {
            var result = await workerService.UpdateHeartbeatAsync(workerId, heartbeat);
            return ResultExtensions.ToHttpResultOrOk(result, "心跳更新成功");
        }).WithSummary("工作节点心跳").WithDescription("工作节点定期发送心跳信息，更新状态和性能指标").AllowAnonymous();

        group.MapPost("/{workerId}/unregister", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.UnregisterWorkerAsync(workerId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点已注销");
        }).WithSummary("工作节点注销").WithDescription("工作节点主动注销，清理相关数据和状态").AllowAnonymous();

        group.MapGet("/", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodeListAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点列表").WithDescription("获取所有工作节点的基本信息和状态概览");

        group.MapGet("/{workerId}", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerByIdAsync(workerId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点详情").WithDescription("获取指定工作节点的详细信息，包括硬件配置和性能指标");

        group.MapGet("/{workerId}/tasks", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetActiveTaskCountAsync(workerId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点任务数").WithDescription("获取指定工作节点当前正在执行的任务数量");

        group.MapPost("/{workerId}/start", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.EnableWorkerNodeAsync(workerId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点已启动");
        }).WithSummary("启动工作节点").WithDescription("启用指定工作节点，使其开始接收和处理任务");

        group.MapPost("/{workerId}/stop", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.DisableWorkerNodeAsync(workerId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点已停止");
        }).WithSummary("停止工作节点").WithDescription("停用指定工作节点，完成当前任务后不再接收新任务");

        group.MapPost("/{workerId}/restart", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.RestartWorkerAsync(workerId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("重启工作节点").WithDescription("重启指定工作节点，先停止再启动以解决问题");

        group.MapPost("/{workerId}/health-check", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerByIdAsync(workerId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("健康检查工作节点").WithDescription("检查指定工作节点的连通性和运行状态");

        group.MapPost("/health-check", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.HealthCheckAllWorkersAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("健康检查所有工作节点").WithDescription("批量检查所有工作节点的健康状态和连通性");

        group.MapPost("/{workerId}/cleanup-files", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.CleanupWorkerFilesAsync(workerId);
            return ResultExtensions.ToHttpResultOrOk(result, "文件清理完成");
        }).WithSummary("清理工作节点文件").WithDescription("清理指定工作节点的临时文件和缓存数据");

        group.MapPost("/cleanup-files", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.CleanupAllWorkerFilesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("清理所有工作节点文件").WithDescription("批量清理所有工作节点的临时文件和缓存数据");

        group.MapPost("/{workerId}/delete", async Task<IResult> (Guid workerId, WorkerService workerService) =>
        {
            var result = await workerService.DeleteWorkerAsync(workerId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("删除工作节点").WithDescription("删除指定工作节点及其相关数据和配置");
    }

    public static void MapAdminProxyEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/proxies").WithTags("Admin Proxy Management").WithValidation();

        group.MapGet("/", async Task<IResult> (ProxyManagementService proxyService,
            int page = 1, int pageSize = 20, string? status = null, string? healthStatus = null) =>
        {
            var result = await proxyService.GetProxyListAsync(page, pageSize, status, healthStatus);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理列表").WithDescription("分页获取代理服务器列表，支持按启用状态和健康状态筛选");

        group.MapGet("/{proxyId}", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.GetProxyDetailAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理详情").WithDescription("获取指定代理服务器的详细配置信息和使用统计");

        group.MapPost("/", async Task<IResult> (CreateProxyRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.CreateProxyAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建代理").WithDescription("添加新的代理服务器到系统代理池");

        group.MapPost("/batch-import", async Task<IResult> (BatchImportProxiesRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.BatchImportProxiesAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("批量导入代理").WithDescription("批量导入多个代理服务器配置到系统代理池");

        group.MapPost("/{proxyId}/update", async Task<IResult> (Guid proxyId, UpdateProxyRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.UpdateProxyAsync(proxyId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已更新");
        }).WithSummary("更新代理").WithDescription("更新指定代理服务器的配置信息和认证凭据");

        group.MapPost("/{proxyId}/delete", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.DeleteProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已删除");
        }).WithSummary("删除代理").WithDescription("从代理池中删除指定的代理服务器");

        group.MapPost("/batch-delete", async Task<IResult> (BatchDeleteProxiesRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.BatchDeleteProxiesAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("批量删除代理").WithDescription("根据ID列表批量删除多个代理服务器");

        group.MapPost("/{proxyId}/enable", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.EnableProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已启用");
        }).WithSummary("启用代理").WithDescription("启用指定代理服务器，使其可用于任务执行");

        group.MapPost("/{proxyId}/disable", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.DisableProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已禁用");
        }).WithSummary("禁用代理").WithDescription("禁用指定代理服务器，停止使用该代理");

        group.MapPost("/{proxyId}/health-check", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.HealthCheckProxyAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("健康检查代理").WithDescription("检查指定代理服务器的连通性和响应性能");

        group.MapPost("/health-check", async Task<IResult> (ProxyManagementService proxyService) =>
        {
            var result = await proxyService.HealthCheckAllProxiesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("健康检查所有代理").WithDescription("批量检查所有代理服务器的健康状态并更新");
    }

    public static void MapAdminContentEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/content").WithTags("Admin Content Management").WithValidation();

        group.MapGet("/", async Task<IResult> (ContentService contentService, int page = 1, int pageSize = 20, string? type = null, string? search = null,
            bool? isBlacklisted = null) =>
        {
            var result = await contentService.GetContentAsync(page, pageSize, type, search, isBlacklisted);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取内容列表").WithDescription("分页获取YouTube内容记录，支持按类型、关键词和黑名单状态筛选");

        group.MapPost("/{contentType}/{contentId}/blacklist", async Task<IResult> (string contentType, string contentId, ContentService contentService) =>
        {
            var result = await contentService.BlacklistContentAsync(contentType, contentId);
            return ResultExtensions.ToHttpResultOrOk(result, "内容已加入黑名单");
        }).WithSummary("加入黑名单").WithDescription("将指定YouTube内容加入黑名单，阻止用户下载");

        group.MapPost("/{contentType}/{contentId}/unblacklist", async Task<IResult> (string contentType, string contentId, ContentService contentService) =>
        {
            var result = await contentService.UnblacklistContentAsync(contentType, contentId);
            return ResultExtensions.ToHttpResultOrOk(result, "内容已移出黑名单");
        }).WithSummary("移出黑名单").WithDescription("将指定YouTube内容从黑名单移除，恢复下载权限");
    }
}