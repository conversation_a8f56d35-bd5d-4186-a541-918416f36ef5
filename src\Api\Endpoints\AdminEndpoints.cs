using Api.Extensions;
using Api.Filters;
using Api.Middleware;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AdminEndpoints
{
    public static void MapAdminAuthEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/auth").WithTags("Admin Auth").WithValidation();

        group.MapPost("/login", async Task<IResult> (AdminLoginRequest request, AdminAuthService adminAuthService) =>
        {
            var result = await adminAuthService.LoginAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("管理员登录").WithDescription("管理员使用用户名和密码登录系统").AllowAnonymous();

        group.MapPost("/logout", async Task<IResult> (AdminAuthService adminAuthService, HttpContext context) =>
        {
            var result = await adminAuthService.LogoutAsync(context);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("管理员登出").WithDescription("管理员退出登录，清除会话信息");

        group.MapGet("/check", async Task<IResult> (AdminAuthService adminAuthService, HttpContext context) =>
        {
            var result = await adminAuthService.CheckAuthStatusAsync(context);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("检查管理员认证状态").WithDescription("验证当前管理员会话是否有效");
    }

    public static void MapAdminDashboardEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/dashboard").WithTags("Admin Dashboard").WithValidation();

        group.MapGet("/", async Task<IResult> (AdminDashboardService dashboardService) =>
        {
            var result = await dashboardService.GetDashboardDataAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取管理员仪表盘数据").WithDescription("获取管理员仪表盘的完整数据，包括系统概览、统计信息等");

        group.MapGet("/stats", async Task<IResult> (AdminDashboardService dashboardService) =>
        {
            var result = await dashboardService.GetKeyStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取关键统计指标").WithDescription("获取系统关键性能指标和统计数据");
    }

    public static void MapAdminUserEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/users").WithTags("Admin User Management").WithValidation();

        group.MapGet("/", async Task<IResult> (AdminUserService userService, int page = 1, int pageSize = 20, string? search = null,
            UserType? userType = null, UserAccountStatus? status = null) =>
        {
            var result = await userService.GetUsersAsync(page, pageSize, search, userType, status);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户列表").WithDescription("分页获取用户列表，支持按用户类型、状态和关键词搜索");

        group.MapGet("/{userId}", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.GetUserDetailAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户详情").WithDescription("获取指定用户的详细信息，包括基本信息、统计数据等");

        group.MapPost("/{userId}/disable", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.DisableUserAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户已禁用");
        }).WithSummary("禁用用户").WithDescription("禁用指定用户账户，用户将无法登录和使用系统");

        group.MapPost("/{userId}/enable", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.EnableUserAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户已启用");
        }).WithSummary("启用用户").WithDescription("启用指定用户账户，恢复用户的正常使用权限");

        group.MapPost("/{userId}/reset-password", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.ResetUserPasswordAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("重置用户密码").WithDescription("重置指定用户的密码，生成临时密码并发送给用户");

        group.MapPost("/{userId}/upgrade-plan", async Task<IResult> (Guid userId, UpgradeUserPlanRequest request, AdminUserService userService) =>
        {
            var result = await userService.UpgradeUserPlanAsync(userId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "用户套餐已升级");
        }).WithSummary("升级用户套餐").WithDescription("升级指定用户的订阅套餐，延长有效期");

        group.MapGet("/{userId}/sessions", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.GetUserSessionsAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户会话").WithDescription("获取指定用户的所有活跃会话信息");

        group.MapPost("/{userId}/sessions/revoke", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.RevokeUserSessionsAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户会话已撤销");
        }).WithSummary("撤销用户会话").WithDescription("撤销指定用户的所有会话，强制用户重新登录");

        group.MapGet("/{userId}/tasks", async Task<IResult> (Guid userId, AdminUserService userService, int page = 1, int pageSize = 20) =>
        {
            var result = await userService.GetUserTasksAsync(userId, page, pageSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户任务").WithDescription("获取指定用户的任务列表，包括任务状态和统计信息");

        group.MapGet("/stats/overview", async Task<IResult> (AdminUserService userService) =>
        {
            var result = await userService.GetUserStatsOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户统计概览").WithDescription("获取用户相关的统计概览，包括总数、活跃度等");
    }

    public static void MapAdminTaskEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/tasks").WithTags("Admin Task Management").WithValidation();

        group.MapGet("/", async Task<IResult> (AdminTaskService taskService, int page = 1, int pageSize = 20, 
            WorkerTaskStatus? status = null, WorkerTaskType? taskType = null, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await taskService.GetTasksAsync(page, pageSize, status, taskType, startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务列表").WithDescription("分页获取系统中的所有任务，支持按状态、类型和时间范围筛选");

        group.MapGet("/{taskId}", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.GetTaskDetailAsync(taskId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务详情").WithDescription("获取指定任务的详细信息，包括执行日志和错误信息");

        group.MapPost("/{taskId}/cancel", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.CancelTaskAsync(taskId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务已取消");
        }).WithSummary("取消任务").WithDescription("取消指定的任务，停止其执行并更新状态");

        group.MapPost("/{taskId}/retry", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.RetryTaskAsync(taskId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务已重试");
        }).WithSummary("重试任务").WithDescription("重新执行失败的任务，重置其状态并重新加入队列");

        group.MapGet("/stats/overview", async Task<IResult> (AdminTaskService taskService) =>
        {
            var result = await taskService.GetTaskStatsOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务统计概览").WithDescription("获取任务相关的统计概览，包括成功率、处理速度等");

        group.MapPost("/cleanup", async Task<IResult> (AdminTaskService taskService, int daysOld = 30) =>
        {
            var result = await taskService.CleanupOldTasksAsync(daysOld);
            return ResultExtensions.ToHttpResultOrOk(result, "任务清理完成");
        }).WithSummary("清理旧任务").WithDescription("清理指定天数之前的已完成任务和相关文件");

        group.MapGet("/pending", async Task<IResult> (TaskService taskService, int batchSize = 10) =>
        {
            var result = await taskService.GetPendingTasksAsync(batchSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取待处理任务").WithDescription("获取系统中待处理的任务列表，用于工作节点调度");

        group.MapPost("/{taskId}/status", async Task<IResult> (Guid taskId, UpdateTaskStatusRequest request, TaskService taskService) =>
        {
            var result = await taskService.UpdateTaskStatusAsync(taskId, request.Status, request.ErrorMessage, request.Progress);
            return ResultExtensions.ToHttpResultOrOk(result, "任务状态更新成功");
        }).WithSummary("更新任务状态").WithDescription("更新指定任务的状态和进度，由工作节点调用");

        group.MapGet("/stats/system", async Task<IResult> (TaskService taskService) =>
        {
            var result = await taskService.GetSystemTaskStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统任务统计").WithDescription("获取整个系统的任务统计信息");

        group.MapPost("/maintenance/cleanup", async Task<IResult> (TaskService taskService, int daysOld = 30) =>
        {
            var result = await taskService.SystemCleanupAsync(daysOld);
            return ResultExtensions.ToHttpResultOrOk(result, "系统清理完成");
        }).WithSummary("系统维护清理").WithDescription("清理系统中的过期任务和文件");
    }

    public static void MapAdminWorkerEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/workers").WithTags("Admin Worker Management").WithValidation();

        group.MapGet("/", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodeListAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点列表").WithDescription("获取所有工作节点的详细信息，包括状态、硬件信息、性能指标等");

        group.MapGet("/{nodeId}", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodeDetailAsync(nodeId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点详情").WithDescription("获取指定工作节点的详细信息和实时状态");

        group.MapPost("/{nodeId}/start", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.StartWorkerNodeAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点已启动");
        }).WithSummary("启动工作节点").WithDescription("启动指定的工作节点，使其开始接收和处理任务");

        group.MapPost("/{nodeId}/stop", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.StopWorkerNodeAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点已停止");
        }).WithSummary("停止工作节点").WithDescription("停止指定的工作节点，完成当前任务后不再接收新任务");

        group.MapPost("/{nodeId}/restart", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.RestartWorkerNodeAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点重启中");
        }).WithSummary("重启工作节点").WithDescription("重启指定的工作节点，先停止再启动");

        group.MapPost("/{nodeId}/drain", async Task<IResult> (Guid nodeId, WorkerNodeManagementService managementService) =>
        {
            var result = await managementService.DrainWorkerNodeAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点开始排空");
        }).WithSummary("排空工作节点").WithDescription("将工作节点设置为排空状态，不再接收新任务并迁移现有任务");

        group.MapPost("/health-check", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.HealthCheckAllWorkersAsync();
            if (result.IsSuccess)
                return ResultExtensions.ApiOk(new { healthyCount = result.Data }, $"健康检查完成，{result.Data} 个节点健康");
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("健康检查所有工作节点").WithDescription("对所有活跃的工作节点执行健康检查，返回健康节点数量");

        group.MapGet("/stats/overview", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodeStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点统计").WithDescription("获取工作节点的基本统计信息，包括总数、在线数、健康状态分布等");

        group.MapGet("/capacity", async Task<IResult> (WorkerNodeManagementService managementService) =>
        {
            var result = await managementService.GetWorkerCapacityAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点容量信息").WithDescription("获取所有在线工作节点的容量和负载信息");

        group.MapPost("/rebalance", async Task<IResult> (WorkerNodeManagementService managementService) =>
        {
            var result = await managementService.RebalanceWorkloadAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "工作负载重新平衡完成");
        }).WithSummary("重新平衡工作负载").WithDescription("在所有工作节点之间重新平衡任务分配");
    }

    public static void MapAdminMonitoringEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/monitoring").WithTags("Admin Monitoring").WithValidation();

        group.MapGet("/dashboard", async Task<IResult> (MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.GetDashboardDataAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取监控面板数据").WithDescription("获取完整的监控面板数据，包括系统概览、节点状态、告警摘要等信息");

        group.MapGet("/system/overview", async Task<IResult> (MonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetSystemOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统概览").WithDescription("获取系统整体状态概览，包括节点统计、资源使用率、任务统计等");

        group.MapGet("/nodes/overview", async Task<IResult> (MonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetWorkersOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取所有节点概览").WithDescription("获取所有工作节点的概览信息，包括状态、健康度、资源使用情况");

        group.MapGet("/nodes/{nodeId}", async Task<IResult> (Guid nodeId, WorkerNodeMonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetNodeRealtimeDataAsync(nodeId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取节点详细信息").WithDescription("获取指定节点的详细信息和实时监控数据");

        group.MapGet("/nodes/{nodeId}/metrics", async Task<IResult> (Guid nodeId, MonitoringService monitoringService, int hours = 24) =>
        {
            var result = await monitoringService.GetWorkerMetricsAsync(nodeId, hours);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取节点指标数据").WithDescription("获取指定节点的历史指标数据，可指定时间范围");

        group.MapGet("/alerts", async Task<IResult> (MonitoringDataService monitoringService, int page = 1, int pageSize = 20) =>
        {
            var result = await monitoringService.GetAlertsAsync(page, pageSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取告警列表").WithDescription("分页获取系统告警列表，按创建时间倒序排列");

        group.MapPost("/alerts/{alertId}/acknowledge", async Task<IResult> (Guid alertId, MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.AcknowledgeAlertAsync(alertId);
            return ResultExtensions.ToHttpResultOrOk(result, "告警已确认");
        }).WithSummary("确认告警").WithDescription("确认指定的告警，标记为已处理状态");

        group.MapPost("/alerts/{alertId}/resolve", async Task<IResult> (Guid alertId, MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.ResolveAlertAsync(alertId);
            return ResultExtensions.ToHttpResultOrOk(result, "告警已解决");
        }).WithSummary("解决告警").WithDescription("解决指定的告警，标记为已解决状态");

        group.MapGet("/performance/overview", async Task<IResult> (MonitoringPerformanceService performanceService) =>
        {
            var result = await performanceService.GetPerformanceOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取性能概览").WithDescription("获取系统整体性能概览，包括CPU、内存、磁盘使用情况");

        group.MapGet("/performance/trends", async Task<IResult> (MonitoringPerformanceService performanceService, int days = 7) =>
        {
            var result = await performanceService.GetPerformanceTrendsAsync(days);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取性能趋势").WithDescription("获取指定天数内的系统性能趋势数据");

        group.MapGet("/realtime/stats", async Task<IResult> (MonitoringRealtimeService realtimeService) =>
        {
            var result = await realtimeService.GetRealtimeStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取实时统计").WithDescription("获取系统实时统计数据，包括当前活跃任务、资源使用等");

        group.MapGet("/health", async Task<IResult> (HealthCheckService healthService) =>
        {
            var result = await healthService.GetSystemHealthAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("系统健康检查").WithDescription("检查系统各组件的健康状态，包括数据库、消息队列、工作节点等");

        group.MapPost("/cleanup/old-data", async Task<IResult> (MonitoringDataService monitoringService) =>
        {
            var result = await monitoringService.CleanupOldDataAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "旧数据清理完成");
        }).WithSummary("清理旧数据").WithDescription("清理过期的监控数据和告警记录");
    }

    public static void MapAdminSystemEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/system").WithTags("Admin System Management").WithValidation();

        group.MapGet("/settings", async Task<IResult> (SystemConfigService configService) =>
        {
            var result = await configService.GetAllSettingsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统设置").WithDescription("获取所有系统配置设置");

        group.MapPost("/settings", async Task<IResult> (UpdateSystemSettingsRequest request, SystemConfigService configService) =>
        {
            var result = await configService.UpdateSettingsAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "系统设置已更新");
        }).WithSummary("更新系统设置").WithDescription("批量更新系统配置设置");

        group.MapGet("/settings/{key}", async Task<IResult> (string key, SystemConfigService configService) =>
        {
            var result = await configService.GetSettingAsync(key);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取特定设置").WithDescription("获取指定键的系统配置值");

        group.MapPost("/settings/{key}", async Task<IResult> (string key, UpdateSettingRequest request, SystemConfigService configService) =>
        {
            var result = await configService.UpdateSettingAsync(key, request);
            return ResultExtensions.ToHttpResultOrOk(result, "设置已更新");
        }).WithSummary("更新特定设置").WithDescription("更新指定键的系统配置值");

        group.MapGet("/features", async Task<IResult> (SystemFeatureService featureService) =>
        {
            var result = await featureService.GetAllFeaturesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取功能开关列表").WithDescription("获取所有系统功能开关的状态");

        group.MapPost("/features/{feature}/enable", async Task<IResult> (string feature, SystemFeatureService featureService) =>
        {
            var result = await featureService.EnableFeatureAsync(feature);
            return ResultExtensions.ToHttpResultOrOk(result, "功能已启用");
        }).WithSummary("启用功能").WithDescription("启用指定的系统功能");

        group.MapPost("/features/{feature}/disable", async Task<IResult> (string feature, SystemFeatureService featureService) =>
        {
            var result = await featureService.DisableFeatureAsync(feature);
            return ResultExtensions.ToHttpResultOrOk(result, "功能已禁用");
        }).WithSummary("禁用功能").WithDescription("禁用指定的系统功能");

        group.MapGet("/limits", async Task<IResult> (SystemLimitService limitService) =>
        {
            var result = await limitService.GetLimitConfigAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取限流配置").WithDescription("获取系统限流和配额配置");

        group.MapPost("/limits", async Task<IResult> (UpdateLimitConfigRequest request, SystemLimitService limitService) =>
        {
            var result = await limitService.UpdateLimitConfigAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "限流配置已更新");
        }).WithSummary("更新限流配置").WithDescription("更新系统限流和配额配置");

        group.MapGet("/info", async Task<IResult> (SystemInfoService infoService) =>
        {
            var result = await infoService.GetSystemInfoAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统信息").WithDescription("获取系统基本信息，包括版本、状态、健康度等");

        group.MapPost("/cache/clear", async Task<IResult> (SystemOperationService operationService) =>
        {
            var result = await operationService.ClearCacheAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "缓存已清理");
        }).WithSummary("清理系统缓存").WithDescription("清理所有系统缓存数据");

        group.MapPost("/restart", async Task<IResult> (SystemOperationService operationService) =>
        {
            var result = await operationService.RestartSystemAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "系统重启已启动");
        }).WithSummary("重启系统").WithDescription("重启整个系统服务");
    }

    public static void MapAdminProxyEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/proxies").WithTags("Admin Proxy Management").WithValidation();

        group.MapGet("/", async Task<IResult> (ProxyManagementService proxyService,
            int page = 1, int pageSize = 20, string? status = null, string? healthStatus = null) =>
        {
            var result = await proxyService.GetProxyListAsync(page, pageSize, status, healthStatus);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理列表").WithDescription("分页获取代理列表，支持按状态和健康状态筛选");

        group.MapGet("/{proxyId}", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.GetProxyDetailAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理详情").WithDescription("获取指定代理的详细信息和使用统计");

        group.MapPost("/", async Task<IResult> (CreateProxyRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.CreateProxyAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建代理").WithDescription("添加新的代理服务器到代理池");

        group.MapPost("/{proxyId}/update", async Task<IResult> (Guid proxyId, UpdateProxyRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.UpdateProxyAsync(proxyId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已更新");
        }).WithSummary("更新代理").WithDescription("更新指定代理的配置信息");

        group.MapPost("/{proxyId}/enable", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.EnableProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已启用");
        }).WithSummary("启用代理").WithDescription("启用指定的代理服务器");

        group.MapPost("/{proxyId}/disable", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.DisableProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已禁用");
        }).WithSummary("禁用代理").WithDescription("禁用指定的代理服务器");

        group.MapPost("/{proxyId}/test", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.TestProxyAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("测试代理").WithDescription("测试指定代理的连通性和性能");

        group.MapPost("/health-check", async Task<IResult> (ProxyManagementService proxyService) =>
        {
            var result = await proxyService.HealthCheckAllProxiesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("健康检查所有代理").WithDescription("对所有代理执行健康检查，更新其状态");

        group.MapGet("/stats/overview", async Task<IResult> (ProxyManagementService proxyService) =>
        {
            var result = await proxyService.GetProxyStatsOverviewAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理统计概览").WithDescription("获取代理池的统计概览，包括总数、可用数、使用率等");

        group.MapGet("/stats/usage", async Task<IResult> (ProxyManagementService proxyService, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await proxyService.GetProxyUsageStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理使用统计").WithDescription("获取指定时间范围内的代理使用统计数据");

        group.MapPost("/rotate", async Task<IResult> (ProxyRotationService rotationService) =>
        {
            var result = await rotationService.RotateProxiesAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "代理轮换完成");
        }).WithSummary("轮换代理").WithDescription("执行代理轮换，重新分配代理使用顺序");

        group.MapPost("/cleanup", async Task<IResult> (ProxyManagementService proxyService) =>
        {
            var result = await proxyService.CleanupFailedProxiesAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "失效代理清理完成");
        }).WithSummary("清理失效代理").WithDescription("清理长期失效的代理服务器");
    }

    public static void MapAdminNotificationEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/notifications").WithTags("Admin Notification Management").WithValidation();

        group.MapPost("/broadcast", async Task<IResult> (BroadcastNotificationRequest request, AdminNotificationService adminNotificationService) =>
        {
            var result = await adminNotificationService.BroadcastNotificationAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("发送广播通知").WithDescription("向所有用户或指定用户群体发送广播通知");

        group.MapPost("/send", async Task<IResult> (SendNotificationRequest request, AdminNotificationService adminNotificationService) =>
        {
            var result = await adminNotificationService.SendNotificationAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("发送通知给指定用户").WithDescription("向指定的用户发送个人通知");

        group.MapGet("/templates", async Task<IResult> (NotificationTemplateService templateService) =>
        {
            var result = await templateService.GetTemplatesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取通知模板列表").WithDescription("获取所有可用的通知模板");

        group.MapPost("/templates", async Task<IResult> (CreateNotificationTemplateRequest request, NotificationTemplateService templateService) =>
        {
            var result = await templateService.CreateTemplateAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建通知模板").WithDescription("创建新的通知模板");

        group.MapPost("/templates/{templateId}/update", async Task<IResult> (Guid templateId, UpdateNotificationTemplateRequest request, NotificationTemplateService templateService) =>
        {
            var result = await templateService.UpdateTemplateAsync(templateId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "模板已更新");
        }).WithSummary("更新通知模板").WithDescription("更新指定的通知模板");

        group.MapGet("/announcements", async Task<IResult> (AnnouncementService announcementService, int page = 1, int pageSize = 10, bool? isActive = null) =>
        {
            var result = await announcementService.GetAnnouncementsAsync(page, pageSize, isActive);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统公告列表").WithDescription("分页获取系统公告，支持按活跃状态筛选");

        group.MapPost("/announcements", async Task<IResult> (CreateAnnouncementRequest request, AnnouncementService announcementService) =>
        {
            var result = await announcementService.CreateAnnouncementAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建系统公告").WithDescription("创建新的系统公告");

        group.MapPost("/announcements/{announcementId}/update", async Task<IResult> (Guid announcementId, UpdateAnnouncementRequest request, AnnouncementService announcementService) =>
        {
            var result = await announcementService.UpdateAnnouncementAsync(announcementId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "公告已更新");
        }).WithSummary("更新系统公告").WithDescription("更新指定的系统公告");

        group.MapPost("/announcements/{announcementId}/activate", async Task<IResult> (Guid announcementId, AnnouncementService announcementService) =>
        {
            var result = await announcementService.ActivateAnnouncementAsync(announcementId);
            return ResultExtensions.ToHttpResultOrOk(result, "公告已激活");
        }).WithSummary("激活系统公告").WithDescription("激活指定的系统公告，使其对用户可见");

        group.MapPost("/announcements/{announcementId}/deactivate", async Task<IResult> (Guid announcementId, AnnouncementService announcementService) =>
        {
            var result = await announcementService.DeactivateAnnouncementAsync(announcementId);
            return ResultExtensions.ToHttpResultOrOk(result, "公告已停用");
        }).WithSummary("停用系统公告").WithDescription("停用指定的系统公告，使其对用户不可见");

        group.MapGet("/settings", async Task<IResult> (NotificationSettingsService settingsService) =>
        {
            var result = await settingsService.GetNotificationSettingsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取通知设置").WithDescription("获取系统通知相关的配置设置");

        group.MapPost("/settings", async Task<IResult> (UpdateNotificationSettingsRequest request, NotificationSettingsService settingsService) =>
        {
            var result = await settingsService.UpdateNotificationSettingsAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "通知设置已更新");
        }).WithSummary("更新通知设置").WithDescription("更新系统通知相关的配置设置");
    }

    public static void MapAdminAnalyticsEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/analytics").WithTags("Admin Analytics").WithValidation();

        group.MapGet("/overview", async Task<IResult> (AnalyticsService analyticsService) =>
        {
            var result = await analyticsService.GetOverviewStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取总体统计概览").WithDescription("获取系统总体统计概览，包括用户、任务、下载等关键指标");

        group.MapGet("/dashboard", async Task<IResult> (AnalyticsService analyticsService) =>
        {
            var result = await analyticsService.GetDashboardStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取仪表盘统计数据").WithDescription("获取管理员仪表盘所需的统计数据");

        group.MapGet("/users", async Task<IResult> (UserAnalyticsService userAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await userAnalyticsService.GetUserStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户统计").WithDescription("获取用户相关的统计数据，支持按时间分组");

        group.MapGet("/users/growth", async Task<IResult> (UserAnalyticsService userAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await userAnalyticsService.GetUserGrowthStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户增长统计").WithDescription("获取指定时间范围内的用户增长趋势数据");

        group.MapGet("/users/activity", async Task<IResult> (UserAnalyticsService userAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await userAnalyticsService.GetUserActivityStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户活跃度统计").WithDescription("获取用户活跃度相关的统计数据");

        group.MapGet("/tasks", async Task<IResult> (TaskAnalyticsService taskAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await taskAnalyticsService.GetTaskStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务统计").WithDescription("获取任务相关的统计数据，支持按时间分组");

        group.MapGet("/tasks/performance", async Task<IResult> (TaskAnalyticsService taskAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await taskAnalyticsService.GetTaskPerformanceStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务性能统计").WithDescription("获取任务执行性能相关的统计数据");

        group.MapGet("/tasks/success-rate", async Task<IResult> (TaskAnalyticsService taskAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await taskAnalyticsService.GetTaskSuccessRateStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务成功率统计").WithDescription("获取任务成功率相关的统计数据");

        group.MapGet("/downloads", async Task<IResult> (DownloadAnalyticsService downloadAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await downloadAnalyticsService.GetDownloadStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取下载统计").WithDescription("获取下载相关的统计数据，支持按时间分组");

        group.MapGet("/downloads/popular", async Task<IResult> (DownloadAnalyticsService downloadAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, int limit = 10) =>
        {
            var result = await downloadAnalyticsService.GetPopularContentStatsAsync(startDate, endDate, limit);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取热门内容统计").WithDescription("获取最受欢迎的下载内容统计数据");

        group.MapGet("/performance", async Task<IResult> (PerformanceAnalyticsService performanceAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await performanceAnalyticsService.GetPerformanceStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统性能统计").WithDescription("获取系统性能相关的统计数据");

        group.MapGet("/realtime", async Task<IResult> (RealTimeAnalyticsService realTimeService) =>
        {
            var result = await realTimeService.GetRealTimeStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取实时统计").WithDescription("获取系统实时统计数据");
    }

    public static void MapAdminContentEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/content").WithTags("Admin Content Management").WithValidation();

        group.MapGet("/blacklist", async Task<IResult> (ContentModerationService moderationService, int page = 1, int pageSize = 20, string? type = null) =>
        {
            var result = await moderationService.GetBlacklistAsync(page, pageSize, type);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取黑名单列表").WithDescription("分页获取内容黑名单，支持按类型筛选");

        group.MapPost("/blacklist", async Task<IResult> (AddToBlacklistRequest request, ContentModerationService moderationService) =>
        {
            var result = await moderationService.AddToBlacklistAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "已添加到黑名单");
        }).WithSummary("添加到黑名单").WithDescription("将指定的视频、频道或播放列表添加到黑名单");

        group.MapPost("/blacklist/{itemId}/remove", async Task<IResult> (Guid itemId, ContentModerationService moderationService) =>
        {
            var result = await moderationService.RemoveFromBlacklistAsync(itemId);
            return ResultExtensions.ToHttpResultOrOk(result, "已从黑名单移除");
        }).WithSummary("从黑名单移除").WithDescription("将指定项目从黑名单中移除");

        group.MapGet("/reports", async Task<IResult> (ContentReportService reportService, int page = 1, int pageSize = 20, string? status = null) =>
        {
            var result = await reportService.GetReportsAsync(page, pageSize, status);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取内容举报列表").WithDescription("分页获取用户举报的内容，支持按处理状态筛选");

        group.MapGet("/reports/{reportId}", async Task<IResult> (Guid reportId, ContentReportService reportService) =>
        {
            var result = await reportService.GetReportDetailAsync(reportId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取举报详情").WithDescription("获取指定举报的详细信息");

        group.MapPost("/reports/{reportId}/approve", async Task<IResult> (Guid reportId, ContentReportService reportService) =>
        {
            var result = await reportService.ApproveReportAsync(reportId);
            return ResultExtensions.ToHttpResultOrOk(result, "举报已批准");
        }).WithSummary("批准举报").WithDescription("批准用户举报，将相关内容加入黑名单");

        group.MapPost("/reports/{reportId}/reject", async Task<IResult> (Guid reportId, ContentReportService reportService) =>
        {
            var result = await reportService.RejectReportAsync(reportId);
            return ResultExtensions.ToHttpResultOrOk(result, "举报已拒绝");
        }).WithSummary("拒绝举报").WithDescription("拒绝用户举报，保持内容可用状态");

        group.MapGet("/dmca", async Task<IResult> (DmcaService dmcaService, int page = 1, int pageSize = 20, string? status = null) =>
        {
            var result = await dmcaService.GetDmcaRequestsAsync(page, pageSize, status);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取DMCA请求列表").WithDescription("分页获取DMCA版权移除请求，支持按状态筛选");

        group.MapGet("/dmca/{requestId}", async Task<IResult> (Guid requestId, DmcaService dmcaService) =>
        {
            var result = await dmcaService.GetDmcaRequestDetailAsync(requestId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取DMCA请求详情").WithDescription("获取指定DMCA请求的详细信息");

        group.MapPost("/dmca/{requestId}/process", async Task<IResult> (Guid requestId, ProcessDmcaRequest request, DmcaService dmcaService) =>
        {
            var result = await dmcaService.ProcessDmcaRequestAsync(requestId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "DMCA请求已处理");
        }).WithSummary("处理DMCA请求").WithDescription("处理DMCA版权移除请求，执行相应的内容移除操作");

        group.MapGet("/copyright/claims", async Task<IResult> (CopyrightService copyrightService, int page = 1, int pageSize = 20) =>
        {
            var result = await copyrightService.GetCopyrightClaimsAsync(page, pageSize);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取版权声明列表").WithDescription("分页获取版权声明列表");

        group.MapPost("/copyright/claim", async Task<IResult> (SubmitCopyrightClaimRequest request, CopyrightService copyrightService) =>
        {
            var result = await copyrightService.SubmitCopyrightClaimAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("提交版权声明").WithDescription("提交新的版权声明");
    }
}
