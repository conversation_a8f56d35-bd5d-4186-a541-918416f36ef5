using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AdminLogService
{
    public async Task<ServiceResult<PagedResponse<SystemLogResponse>>> GetSystemLogsAsync(int page, int pageSize, string? level, DateTime? startTime,
        DateTime? endTime)
    {
        await Task.Delay(1);
        return ServiceResult<PagedResponse<SystemLogResponse>>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<PagedResponse<ErrorLogResponse>>> GetErrorLogsAsync(int page, int pageSize, DateTime? startTime, DateTime? endTime)
    {
        await Task.Delay(1);
        return ServiceResult<PagedResponse<ErrorLogResponse>>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<PagedResponse<AuditLogResponse>>> GetAuditLogsAsync(int page, int pageSize, string? action, Guid? userId,
        DateTime? startTime, DateTime? endTime)
    {
        await Task.Delay(1);
        return ServiceResult<PagedResponse<AuditLogResponse>>.Failure("功能暂未实现");
    }
}