using Shared.Common;
using Shared.Messages;

namespace Worker.Services;

/// <summary>
///     任务处理服务
/// </summary>
public class TaskProcessorService
{
    private readonly IConfiguration _configuration;
    private readonly string _downloadPath;
    private readonly FFmpegService _ffmpegService;
    private readonly ILogger<TaskProcessorService> _logger;
    private readonly YtDlpService _ytDlpService;

    public TaskProcessorService(ILogger<TaskProcessorService> logger, YtDlpService ytDlpService, FFmpegService ffmpegService, IConfiguration configuration)
    {
        _logger = logger;
        _ytDlpService = ytDlpService;
        _ffmpegService = ffmpegService;
        _configuration = configuration;
        _downloadPath = InitializeDownloadPath();
    }

    /// <summary>
    ///     处理视频下载任务
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessVideoDownloadAsync(TaskProcessRequest request, IProgress<TaskProgressInfo>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始视频下载任�? {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(10, "正在获取视频信息..."));

            // 1. 获取视频信息
            var videoInfoResult = await _ytDlpService.FetchVideoAsync(request.VideoId, request.Proxy);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure($"获取视频信息失败: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            progress?.Report(new TaskProgressInfo(30, "正在下载视频..."));

            // 2. 创建下载选项并下载视�?            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            var downloadOptions = new VideoDownloadOptions(request.VideoId, taskDownloadPath, request.OutputFormat, request.Quality, request.StartTime,
                request.EndTime, request.Proxy);

            var downloadResult = await _ytDlpService.DownloadVideoAsync(downloadOptions, cancellationToken);
            if (!downloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(downloadResult.ErrorMessage!, downloadResult.ErrorCode);

            var filePath = downloadResult.Data!.FilePath;

            // 3. 如果需要剪辑，进行剪辑处理
            if (request.StartTime.HasValue || request.EndTime.HasValue)
            {
                progress?.Report(new TaskProgressInfo(70, "正在剪辑视频..."));

                var clippedPath = Path.ChangeExtension(filePath, $".clipped{Path.GetExtension(filePath)}");
                var clipResult = await _ffmpegService.ClipVideoAsync(filePath, clippedPath, request.StartTime, request.EndTime, cancellationToken);

                if (!clipResult.IsSuccess)
                    return ServiceResult<TaskProcessResult>.Failure(clipResult.ErrorMessage!, clipResult.ErrorCode);

                // 删除原始文件，使用剪辑后的文�?                try
                {
                    File.Delete(filePath);
                }
                catch
                {
                }

                filePath = clipResult.Data!.FilePath;
            }

            progress?.Report(new TaskProgressInfo(100, "任务完成"));

            var result = new TaskProcessResult(filePath, new FileInfo(filePath).Length, TimeSpan.Zero, // TODO: 从视频信息中获取
                request.OutputFormat ?? "mp4");

            _logger.LogInformation("视频下载任务完成: {TaskId}，文件: {FilePath}", request.TaskId, filePath);

            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("视频下载任务已取消: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure("任务已取消", "TASK_CANCELLED");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理视频下载任务时发生错误: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"处理任务时发生错误: {ex.Message}");
        }
    }


    /// <summary>
    ///     处理音频转换任务
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessAudioConvertAsync(TaskProcessRequest request, IProgress<TaskProgressInfo>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始音频转换任�? {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(10, "正在获取音频信息..."));

            // 1. 创建下载选项并下载音�?            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            var downloadOptions = new AudioDownloadOptions(request.VideoId, taskDownloadPath, request.OutputFormat, request.Quality, request.StartTime,
                request.EndTime, request.Proxy);

            progress?.Report(new TaskProgressInfo(30, "正在下载音频..."));

            var downloadResult = await _ytDlpService.DownloadAudioAsync(downloadOptions, cancellationToken);
            if (!downloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(downloadResult.ErrorMessage!, downloadResult.ErrorCode);

            var filePath = downloadResult.Data!.FilePath;

            // 2. 如果需要剪辑，进行剪辑处理
            if (request.StartTime.HasValue || request.EndTime.HasValue)
            {
                progress?.Report(new TaskProgressInfo(70, "正在剪辑音频..."));

                var clippedPath = Path.ChangeExtension(filePath, $".clipped{Path.GetExtension(filePath)}");
                var clipResult = await _ffmpegService.ClipAudioAsync(filePath, clippedPath, request.StartTime, request.EndTime, cancellationToken);

                if (!clipResult.IsSuccess)
                    return ServiceResult<TaskProcessResult>.Failure(clipResult.ErrorMessage!, clipResult.ErrorCode);

                // 删除原始文件，使用剪辑后的文�?                try
                {
                    File.Delete(filePath);
                }
                catch
                {
                }

                filePath = clipResult.Data!.FilePath;
            }

            progress?.Report(new TaskProgressInfo(100, "音频转换完成"));

            var result = new TaskProcessResult(filePath, new FileInfo(filePath).Length, TimeSpan.Zero, request.OutputFormat ?? "mp3");

            _logger.LogInformation("音频转换任务完成: {TaskId}，文�? {FilePath}", request.TaskId, filePath);

            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理音频转换任务时发生错�? {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"处理音频转换任务时发生错�? {ex.Message}");
        }
    }

    /// <summary>
    ///     处理缩略图下载任�?    ///
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessThumbnailDownloadAsync(TaskProcessRequest request, IProgress<TaskProgressInfo>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始缩略图下载任务: {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(30, "正在下载缩略�?.."));

            // 创建下载选项并下载缩略图
            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            var downloadOptions = new ThumbnailDownloadOptions(request.VideoId, taskDownloadPath, request.OutputFormat, request.Proxy);

            var downloadResult = await _ytDlpService.DownloadThumbnailAsync(downloadOptions, cancellationToken);
            if (!downloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(downloadResult.ErrorMessage!, downloadResult.ErrorCode);

            progress?.Report(new TaskProgressInfo(100, "缩略图下载完�?));

            var result = new TaskProcessResult(downloadResult.Data!.FilePath, downloadResult.Data.FileSize, TimeSpan.Zero, downloadResult.Data.Format);

            _logger.LogInformation("缩略图下载任务完�? {TaskId}，文�? {FilePath}", request.TaskId, downloadResult.Data.FilePath);

            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理缩略图下载任务时发生错误: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"处理缩略图下载任务时发生错误: {ex.Message}");
        }
    }


    /// <summary>
    ///     处理GIF创建任务
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessGifCreateAsync(TaskProcessRequest request, IProgress<TaskProgressInfo>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始GIF创建任务: {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(10, "正在下载视频..."));

            // 1. 先下载视频用于处�?            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            var downloadOptions = new VideoDownloadOptions(request.VideoId, taskDownloadPath, "mp4", // 固定使用mp4格式用于GIF制作
                "720p", // 限制分辨率以减少处理时间
                null, null, // 不在下载时剪辑，在GIF制作时处�?                request.Proxy
            );

            var videoDownloadResult = await _ytDlpService.DownloadVideoAsync(downloadOptions, cancellationToken);
            if (!videoDownloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(videoDownloadResult.ErrorMessage!, videoDownloadResult.ErrorCode);

            var videoPath = videoDownloadResult.Data!.FilePath;

            progress?.Report(new TaskProgressInfo(60, "正在创建GIF..."));

            // 2. 解析GIF参数
            var startTime = request.StartTime ?? 0;
            var endTime = request.EndTime ?? startTime + 10; // 默认10�?            var fps = 10; // 默认帧率
            var width = 400; // 默认宽度

            // 从Quality参数解析帧率和宽�?(格式: "fps:width" �?"10:400")
            if (!string.IsNullOrEmpty(request.Quality))
            {
                var parts = request.Quality.Split(':');
                if (parts.Length >= 1 && int.TryParse(parts[0], out var parsedFps))
                    fps = parsedFps;
                if (parts.Length >= 2 && int.TryParse(parts[1], out var parsedWidth))
                    width = parsedWidth;
            }

            // 3. 使用FFmpeg创建GIF
            var gifPath = Path.Combine(taskDownloadPath, $"{request.TaskId}_animated.gif");
            var gifResult = await _ffmpegService.CreateGifAsync(videoPath, gifPath, startTime, endTime, fps, width, cancellationToken);

            // 清理临时视频文件
            try
            {
                File.Delete(videoPath);
            }
            catch
            {
            }

            if (!gifResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(gifResult.ErrorMessage!, gifResult.ErrorCode);

            progress?.Report(new TaskProgressInfo(100, "GIF创建完成"));

            var result = new TaskProcessResult(gifResult.Data!.FilePath, gifResult.Data.FileSize, gifResult.Data.Duration ?? TimeSpan.Zero,
                gifResult.Data.Format);

            _logger.LogInformation("GIF创建任务完成: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "GIF创建任务失败: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"GIF创建失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     处理评论下载任务
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessCommentDownloadAsync(TaskProcessRequest request, IProgress<TaskProgressInfo>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始评论下载任�? {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(20, "正在获取评论..."));

            // 创建下载选项并下载评�?            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            // 解析评论数量限制（从Quality参数中获取，默认100条）
            var commentLimit = 100;
            if (!string.IsNullOrEmpty(request.Quality) && int.TryParse(request.Quality, out var limit))
                commentLimit = Math.Min(limit, 1000); // 最�?000�?
            var downloadOptions = new CommentDownloadOptions(request.VideoId, taskDownloadPath, commentLimit, request.OutputFormat,
                "top", // 默认按热门排�?                request.Proxy
            );

            progress?.Report(new TaskProgressInfo(50, "正在下载评论..."));

            var downloadResult = await _ytDlpService.DownloadCommentsAsync(downloadOptions, cancellationToken);
            if (!downloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(downloadResult.ErrorMessage!, downloadResult.ErrorCode);

            progress?.Report(new TaskProgressInfo(100, "评论下载完成"));

            var result = new TaskProcessResult(downloadResult.Data!.FilePath, downloadResult.Data.FileSize, TimeSpan.Zero, downloadResult.Data.Format);

            _logger.LogInformation("评论下载任务完成: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "评论下载任务失败: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"评论下载失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     处理描述下载任务
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessDescriptionDownloadAsync(TaskProcessRequest request,
        IProgress<TaskProgressInfo>? progress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始描述下载任�? {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(30, "正在获取视频信息..."));

            // 创建下载选项并下载描�?            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            var downloadOptions = new DescriptionDownloadOptions(request.VideoId, taskDownloadPath, request.Proxy);

            progress?.Report(new TaskProgressInfo(70, "正在保存描述..."));

            var downloadResult = await _ytDlpService.DownloadDescriptionAsync(downloadOptions, cancellationToken);
            if (!downloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(downloadResult.ErrorMessage!, downloadResult.ErrorCode);

            progress?.Report(new TaskProgressInfo(100, "描述下载完成"));

            var result = new TaskProcessResult(downloadResult.Data!.FilePath, downloadResult.Data.FileSize, TimeSpan.Zero, downloadResult.Data.Format);

            _logger.LogInformation("描述下载任务完成: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "描述下载任务失败: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"描述下载失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     处理铃声制作任务
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessRingtoneCreateAsync(TaskProcessRequest request, IProgress<TaskProgressInfo>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始铃声制作任�? {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(10, "正在下载音频..."));

            // 1. 先下载音�?            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            var downloadOptions = new AudioDownloadOptions(request.VideoId, taskDownloadPath, "mp3", // 固定使用mp3格式用于铃声制作
                "medium", // 中等质量
                null, null, // 不在下载时剪辑，在铃声制作时处理
                request.Proxy);

            var audioDownloadResult = await _ytDlpService.DownloadAudioAsync(downloadOptions, cancellationToken);
            if (!audioDownloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(audioDownloadResult.ErrorMessage!, audioDownloadResult.ErrorCode);

            var audioPath = audioDownloadResult.Data!.FilePath;

            progress?.Report(new TaskProgressInfo(60, "正在制作铃声..."));

            // 2. 创建铃声
            var startTime = request.StartTime ?? 0;
            var endTime = request.EndTime ?? startTime + 30; // 默认30�?            var format = request.OutputFormat?.ToLower() ?? "m4r";
            var extension = format == "mp3" ? "mp3" : "m4r";
            var ringtonePath = Path.Combine(taskDownloadPath, $"{request.TaskId}_ringtone.{extension}");

            var ringtoneResult = await _ffmpegService.CreateRingtoneAsync(audioPath, ringtonePath, startTime, endTime, format, cancellationToken);

            // 清理临时音频文件
            try
            {
                File.Delete(audioPath);
            }
            catch
            {
            }

            if (!ringtoneResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(ringtoneResult.ErrorMessage!, ringtoneResult.ErrorCode);

            progress?.Report(new TaskProgressInfo(100, "铃声制作完成"));

            var result = new TaskProcessResult(ringtoneResult.Data!.FilePath, ringtoneResult.Data.FileSize, ringtoneResult.Data.Duration ?? TimeSpan.Zero,
                ringtoneResult.Data.Format);

            _logger.LogInformation("铃声制作任务完成: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "铃声制作任务失败: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"铃声制作失败: {ex.Message}");
        }
    }


    /// <summary>
    ///     处理字幕下载任务
    /// </summary>
    public async Task<ServiceResult<TaskProcessResult>> ProcessSubtitleDownloadAsync(TaskProcessRequest request, IProgress<TaskProgressInfo>? progress = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始字幕下载任�? {TaskId}，视�? {VideoId}", request.TaskId, request.VideoId);

            progress?.Report(new TaskProgressInfo(20, "正在获取字幕信息..."));

            // 创建下载选项并下载字�?            var taskDownloadPath = Path.Combine(_downloadPath, request.TaskId.ToString());
            Directory.CreateDirectory(taskDownloadPath);

            // 解析字幕语言（从Quality参数中获取，默认为自动检测）
            var subtitleLang = string.IsNullOrEmpty(request.Quality) ? "auto" : request.Quality;
            var outputFormat = request.OutputFormat?.ToLower() ?? "srt";

            // 验证输出格式
            if (!new[] { "srt", "vtt", "ass", "lrc" }.Contains(outputFormat))
                return ServiceResult<TaskProcessResult>.Failure("不支持的字幕格式，支持的格式: srt, vtt, ass, lrc");

            var downloadOptions = new SubtitleDownloadOptions(request.VideoId, taskDownloadPath, subtitleLang, outputFormat,
                subtitleLang == "auto" || subtitleLang.Contains("auto"), // AutoGenerated
                request.Proxy);

            progress?.Report(new TaskProgressInfo(50, "正在下载字幕..."));

            var downloadResult = await _ytDlpService.DownloadSubtitleAsync(downloadOptions, cancellationToken);
            if (!downloadResult.IsSuccess)
                return ServiceResult<TaskProcessResult>.Failure(downloadResult.ErrorMessage!, downloadResult.ErrorCode);

            progress?.Report(new TaskProgressInfo(100, "字幕下载完成"));

            var result = new TaskProcessResult(downloadResult.Data!.FilePath, downloadResult.Data.FileSize, TimeSpan.Zero, downloadResult.Data.Format);

            _logger.LogInformation("字幕下载任务完成: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "字幕下载任务失败: {TaskId}", request.TaskId);
            return ServiceResult<TaskProcessResult>.Failure($"字幕下载失败: {ex.Message}");
        }
    }


    /// <summary>
    ///     初始化下载路径，包含权限检查和错误处理
    /// </summary>
    private string InitializeDownloadPath()
    {
        try
        {
            // 获取配置的下载路径，如果没有配置则使用默认路�?            var configuredPath = _configuration["Worker:DownloadPath"];
            string downloadPath;

            if (!string.IsNullOrEmpty(configuredPath))
            {
                // 使用配置的路�?                downloadPath = Path.GetFullPath(configuredPath);
                _logger.LogInformation("使用配置的下载路�? {DownloadPath}", downloadPath);
            }
            else
            {
                // 使用默认路径：优先使用用户文档目录，其次使用临时目录
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                if (!string.IsNullOrEmpty(documentsPath) && Directory.Exists(documentsPath))
                {
                    downloadPath = Path.Combine(documentsPath, "YTDownloader", "Downloads");
                    _logger.LogInformation("使用默认下载路径（文档目录）: {DownloadPath}", downloadPath);
                }
                else
                {
                    downloadPath = Path.Combine(Path.GetTempPath(), "ytdownloader");
                    _logger.LogWarning("使用临时目录作为下载路径: {DownloadPath}", downloadPath);
                }
            }

            // 确保目录存在
            if (!Directory.Exists(downloadPath))
            {
                Directory.CreateDirectory(downloadPath);
                _logger.LogInformation("创建下载目录: {DownloadPath}", downloadPath);
            }

            // 测试写入权限
            var testFile = Path.Combine(downloadPath, $"test_{Guid.NewGuid()}.tmp");
            try
            {
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
                _logger.LogDebug("下载目录权限检查通过: {DownloadPath}", downloadPath);
            }
            catch (UnauthorizedAccessException)
            {
                _logger.LogError("下载目录没有写入权限: {DownloadPath}", downloadPath);
                throw new InvalidOperationException($"下载目录没有写入权限: {downloadPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载目录权限检查失�? {DownloadPath}", downloadPath);
                throw new InvalidOperationException($"下载目录权限检查失�? {downloadPath}", ex);
            }

            return downloadPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化下载路径失�?);
            throw new InvalidOperationException("初始化下载路径失�?, ex);
        }
    }
}