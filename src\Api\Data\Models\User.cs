﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Shared.Common;

namespace Api.Data.Models;

public class User
{
    public Guid Id { get; init; }
    public required UserType UserType { get; set; }
    public string? Email { get; set; }
    public bool EmailVerified { get; set; }
    public string? EmailVerificationToken { get; set; }
    public DateTime? EmailVerificationTokenExpiresAt { get; set; }
    public string? PasswordResetToken { get; set; }
    public DateTime? PasswordResetTokenExpiresAt { get; set; }
    public string? PasswordHash { get; set; }
    public required UserPlanType PlanType { get; set; } = UserPlanType.Free;
    public DateTime? PlanExpiresAt { get; set; }
    public required UserAccountStatus Status { get; set; } = UserAccountStatus.Active;
    public DateTime CreatedAt { get; init; }
    public DateTime LastActiveAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public ICollection<WorkerTask> WorkerTasks { get; set; } = [];
    public ICollection<BatchTask> BatchTasks { get; set; } = [];
}

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("users", "public");
        builder.HasKey(u => u.Id);
        builder.Property(u => u.Id).ValueGeneratedNever();
        builder.Property(u => u.UserType).IsRequired().HasColumnName("user_type").HasConversion<string>();
        builder.Property(u => u.Email).HasMaxLength(255).HasColumnName("email");
        builder.Property(u => u.EmailVerified).IsRequired().HasColumnName("email_verified").HasDefaultValue(false);
        builder.Property(u => u.EmailVerificationToken).HasMaxLength(255).HasColumnName("email_verification_token");
        builder.Property(u => u.EmailVerificationTokenExpiresAt).HasColumnName("email_verification_token_expires_at");
        builder.Property(u => u.PasswordResetToken).HasMaxLength(255).HasColumnName("password_reset_token");
        builder.Property(u => u.PasswordResetTokenExpiresAt).HasColumnName("password_reset_token_expires_at");
        builder.Property(u => u.PasswordHash).HasMaxLength(255).HasColumnName("password_hash");
        builder.Property(u => u.PlanType).IsRequired().HasColumnName("plan_type").HasDefaultValue(UserPlanType.Free).HasConversion<string>();
        builder.Property(u => u.PlanExpiresAt).HasColumnName("plan_expires_at");
        builder.Property(u => u.Status).IsRequired().HasColumnName("status").HasDefaultValue(UserAccountStatus.Active).HasConversion<string>();
        builder.Property(u => u.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(u => u.LastActiveAt).IsRequired().HasColumnName("last_active_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(u => u.UpdatedAt).IsRequired().HasColumnName("updated_at").HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.HasIndex(u => u.Email).IsUnique().HasFilter("email IS NOT NULL");
        builder.HasIndex(u => u.UserType).HasDatabaseName("ix_users_user_type");
        builder.HasIndex(u => u.Status).HasDatabaseName("ix_users_status");
        builder.HasIndex(u => u.LastActiveAt).HasDatabaseName("ix_users_last_active_at");
    }
}