namespace Shared.Common;

public enum UserType
{
    Anonymous = 0,
    Registered = 1
}

public enum UserPlanType
{
    Free = 0,
    Premium = 1,
    Enterprise = 2
}

public enum UserAccountStatus
{
    Active = 0,
    Disabled = 1,
    
    Deleted = 3
}

public enum WorkerTaskType
{
    VideoDownload = 0,
    AudioConvert = 1,
    GifCreate = 2,
    SubtitleDownload = 3,
    ThumbnailDownload = 4,
    CommentDownload = 5,
    DescriptionDownload = 6,
    RingtoneCreate = 7
}

public enum WorkerTaskStatus
{
    Pending = 0,
    Queued = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Cancelled = 5
}

public enum WorkerTaskPriority
{
    Low = 1,
    Normal = 5,
    High = 8,
    Critical = 10
}

public enum BatchTaskType
{
    Playlist = 0,
    Channel = 1,
    MultipleVideos = 2
}

public enum BatchTaskStatus
{
    Created = 0,
    Fetching = 1,
    Ready = 2,
    Running = 3,
    Paused = 4,
    Completed = 5,
    Failed = 6,
    Cancelled = 7
}

public enum WorkerStatus
{
    Offline = 0,
    Online = 1,
    Busy = 2,
    Draining = 3,
    Maintenance = 4
}

public enum WorkerHealthStatus
{
    Unknown = 0,
    Healthy = 1,
    Warning = 2,
    Critical = 3
}

public enum WorkerAlertLevel
{
    Info = 0,
    Warning = 1,
    Critical = 2
}

public enum WorkerAlertType
{
    CpuUsage = 0,
    MemoryUsage = 1,
    DiskUsage = 2,
    NodeOffline = 3,
    TaskFailure = 4,
    SystemError = 5
}

public enum ErrorSeverity
{
    Low = 0,
    Medium = 1,
    High = 2,
    Critical = 3
}

public enum ProxyType
{
    Http = 0,
    Https = 1,
    Socks5 = 2
}

public enum ProxyStatus
{
    Active = 0,
    Inactive = 1,
    Failed = 2,
    Maintenance = 3
}

public enum ProxyHealthStatus
{
    Healthy = 0,
    Unhealthy = 1,
    Unknown = 2
}

public enum YouTubeContentType
{
    Video = 0,
    Playlist = 1,
    Channel = 2
}

public enum VideoQuality
{
    Worst = 1,
    P144 = 144,
    P240 = 240,
    P360 = 360,
    P480 = 480,
    P720 = 720,
    P1080 = 1080,
    P1440 = 1440,
    P2160 = 2160,
    Best = 9999
}

public enum AudioQuality
{
    K64 = 64,
    K128 = 128,
    K192 = 192,
    K256 = 256,
    K320 = 320,
    Best = 9999
}