using Api.Data;
using Api.Data.Models;
using Api.Services;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Middleware;

public class AuthenticationMiddleware
{
    public const string ANONYMOUS_COOKIE_NAME = "anonymous_id";
    public const string SESSION_COOKIE_NAME = "session_id";
    public const string USER_CONTEXT_KEY = "CurrentUser";
    private readonly ILogger<AuthenticationMiddleware> _logger;
    private readonly RequestDelegate _next;

    public AuthenticationMiddleware(RequestDelegate next, ILogger<AuthenticationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, AppDbContext dbContext, AuthService authService)
    {
        try
        {
            var currentUser = await GetOrCreateCurrentUserAsync(context, dbContext, authService);

            // 将用户信息存储到HttpContext中，供后续中间件和控制器使用
            context.Items[USER_CONTEXT_KEY] = currentUser;

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authentication middleware error");
            await _next(context);
        }
    }

    /// <summary>
    ///     获取或创建当前用户
    /// </summary>
    private async Task<CurrentUser> GetOrCreateCurrentUserAsync(HttpContext context, AppDbContext dbContext, AuthService authService)
    {
        // 1. 首先检查是否有会话Cookie（注册用户）
        if (context.Request.Cookies.TryGetValue(SESSION_COOKIE_NAME, out var sessionToken) && !string.IsNullOrEmpty(sessionToken))
        {
            var session = await authService.ValidateSessionAsync(sessionToken);

            if (session != null)
                return new CurrentUser(session.User.Id, session.User.UserType, session.User.Email, session.User.PlanType, false);

            // 会话无效，清除Cookie
            context.Response.Cookies.Delete(SESSION_COOKIE_NAME);
        }

        // 2. 检查匿名用户Cookie
        if (context.Request.Cookies.TryGetValue(ANONYMOUS_COOKIE_NAME, out var anonymousId) && Guid.TryParse(anonymousId, out var anonymousUserId))
        {
            var anonymousUser = await dbContext.Users
                .Where(u => u.Id == anonymousUserId && u.UserType == UserType.Anonymous && u.Status == UserAccountStatus.Active).FirstOrDefaultAsync();

            if (anonymousUser != null)
                return new CurrentUser(anonymousUser.Id, anonymousUser.UserType, null, anonymousUser.PlanType, true);
        }

        // 3. 创建新的匿名用户
        var newAnonymousUser = new User
        {
            Id = Guid.NewGuid(),
            UserType = UserType.Anonymous,
            PlanType = UserPlanType.Free,
            Status = UserAccountStatus.Active,
            CreatedAt = DateTime.UtcNow,
            LastActiveAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        dbContext.Users.Add(newAnonymousUser);
        await dbContext.SaveChangesAsync();

        // 设置匿名用户Cookie（有效期1年）
        var cookieOptions = new CookieOptions
        {
            HttpOnly = true,
            Secure = context.Request.IsHttps,
            SameSite = SameSiteMode.Lax,
            Expires = DateTimeOffset.UtcNow.AddYears(1)
        };

        context.Response.Cookies.Append(ANONYMOUS_COOKIE_NAME, newAnonymousUser.Id.ToString(), cookieOptions);

        _logger.LogInformation("Created new anonymous user: {UserId}", newAnonymousUser.Id);

        return new CurrentUser(newAnonymousUser.Id, newAnonymousUser.UserType, null, newAnonymousUser.PlanType, true);
    }

    /// <summary>
    ///     更新用户最后活跃时间
    /// </summary>
    private async Task UpdateUserLastActiveAsync(AppDbContext dbContext, Guid userId)
    {
        try
        {
            await dbContext.Users.Where(u => u.Id == userId)
                .ExecuteUpdateAsync(u => u.SetProperty(x => x.LastActiveAt, DateTime.UtcNow).SetProperty(x => x.UpdatedAt, DateTime.UtcNow));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update user last active time for user: {UserId}", userId);
        }
    }
}

/// <summary>
///     HttpContext扩展方法，用于获取当前用户
/// </summary>
public static class HttpContextExtensions
{
    /// <summary>
    ///     获取当前用户信息
    /// </summary>
    public static CurrentUser? GetCurrentUser(this HttpContext context)
    {
        return context.Items[AuthenticationMiddleware.USER_CONTEXT_KEY] as CurrentUser;
    }

    /// <summary>
    ///     获取当前用户ID
    /// </summary>
    public static Guid? GetCurrentUserId(this HttpContext context)
    {
        return context.GetCurrentUser()?.UserId;
    }

    /// <summary>
    ///     检查当前用户是否为注册用户
    /// </summary>
    public static bool IsRegisteredUser(this HttpContext context)
    {
        var user = context.GetCurrentUser();
        return user != null && user.IsRegistered;
    }

    /// <summary>
    ///     检查当前用户是否为高级用户
    /// </summary>
    public static bool IsPremiumUser(this HttpContext context)
    {
        var user = context.GetCurrentUser();
        return user != null && user.IsPremium;
    }
}