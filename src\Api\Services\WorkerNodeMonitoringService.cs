using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class WorkerNodeMonitoringService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<WorkerNodeMonitoringService> _logger;
    private readonly MonitoringService _monitoringService;

    public WorkerNodeMonitoringService(AppDbContext dbContext, MonitoringService monitoringService, ILogger<WorkerNodeMonitoringService> logger)
    {
        _dbContext = dbContext;
        _monitoringService = monitoringService;
        _logger = logger;
    }

    public async Task<ServiceResult<List<WorkerOverviewResponse>>> GetNodesOverviewAsync()
    {
        try
        {
            return await _monitoringService.GetWorkersOverviewAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取节点概览时发生错误");
            return ServiceResult<List<WorkerOverviewResponse>>.Failure("获取节点概览失败", "NODES_OVERVIEW_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerRealtimeDataResponse>> GetNodeRealtimeDataAsync(Guid nodeId)
    {
        try
        {
            var worker = await _dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult<WorkerRealtimeDataResponse>.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            // 获取最新的指标数据
            var latestMetrics = await _dbContext.WorkerMetrics.Where(m => m.WorkerId == nodeId).OrderByDescending(m => m.RecordedAt).FirstOrDefaultAsync();

            // 获取活跃告警数量
            var activeAlertsCount = await _dbContext.WorkerAlerts.Where(a => a.WorkerId == nodeId && !a.IsResolved).CountAsync();

            // 获取当前任务数量
            var currentTasks = await _dbContext.WorkerTasks
                .Where(t => t.AssignedWorkerId == nodeId && (t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued)).CountAsync();

            var realtimeData = new WorkerRealtimeDataResponse(nodeId, worker.Name, worker.Status, worker.HealthStatus, worker.LastActiveAt,
                latestMetrics?.CpuUsagePercent ?? 0, latestMetrics?.MemoryUsagePercent ?? 0, latestMetrics?.DiskUsagePercent ?? 0,
                latestMetrics?.NetworkReceivedGB ?? 0, latestMetrics?.NetworkSentGB ?? 0, latestMetrics?.NetworkBandwidthMbps ?? 0,
                latestMetrics?.ActiveConnections ?? 0, currentTasks, activeAlertsCount, latestMetrics?.RecordedAt ?? DateTime.UtcNow);

            return ServiceResult<WorkerRealtimeDataResponse>.Success(realtimeData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取节点 {NodeId} 实时数据时发生错误", nodeId);
            return ServiceResult<WorkerRealtimeDataResponse>.Failure("获取节点实时数据失败", "NODE_REALTIME_ERROR");
        }
    }

    public async Task<ServiceResult<WorkerHistoryResponse>> GetNodeHistoryDataAsync(Guid nodeId, DateTime? startTime = null, DateTime? endTime = null)
    {
        try
        {
            var worker = await _dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult<WorkerHistoryResponse>.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            var start = startTime ?? DateTime.UtcNow.AddHours(-24);
            var end = endTime ?? DateTime.UtcNow;

            return await _monitoringService.GetWorkerHistoryAsync(nodeId, start, end);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取节点 {NodeId} 历史数据时发生错误", nodeId);
            return ServiceResult<WorkerHistoryResponse>.Failure("获取节点历史数据失败", "NODE_HISTORY_ERROR");
        }
    }
}