using Shared.Common;

namespace Shared.DTOs;

// ==================== 付费计划 ====================

public record BillingPlanResponse(
    Guid Id,
    string Name,
    string Description,
    decimal Price,
    string Currency,
    string BillingCycle,
    List<string> Features,
    bool IsActive);

public record PlanComparisonResponse(List<BillingPlanResponse> Plans, List<FeatureComparisonResponse> FeatureComparison);

public record FeatureComparisonResponse(string FeatureName, Dictionary<string, object> PlanValues);

// ==================== 订阅管理 ====================

public record SubscriptionResponse(
    Guid Id,
    Guid PlanId,
    string PlanName,
    string Status,
    DateTime StartDate,
    DateTime? EndDate,
    DateTime? NextBillingDate,
    decimal Amount,
    string Currency);

public record CreateSubscriptionRequest(Guid PlanId, string PaymentMethodId);

public record UpgradeSubscriptionRequest(Guid NewPlanId, bool ProrateBilling);

public record DowngradeSubscriptionRequest(Guid NewPlanId, DateTime? EffectiveDate);

public record CancelSubscriptionRequest(string Reason, DateTime? EffectiveDate);

// ==================== 支付管理 ====================

public record PaymentResponse(
    Guid Id,
    decimal Amount,
    string Currency,
    string Status,
    string PaymentMethod,
    DateTime CreatedAt,
    DateTime? ProcessedAt,
    string? TransactionId);

public record CreatePaymentRequest(decimal Amount, string Currency, string PaymentMethodId, Guid? SubscriptionId);

public record ConfirmPaymentRequest(string PaymentIntentId);

public record RefundPaymentRequest(string Reason, decimal? Amount);

// ==================== 发票管理 ====================

public record InvoiceResponse(
    Guid Id,
    string InvoiceNumber,
    decimal Amount,
    string Currency,
    string Status,
    DateTime IssuedAt,
    DateTime? PaidAt,
    DateTime DueDate,
    string DownloadUrl);

// ==================== 使用量统计 ====================

public record UsageResponse(
    Dictionary<string, int> CurrentUsage,
    Dictionary<string, int> Limits,
    Dictionary<string, double> UsagePercentage,
    DateTime PeriodStart,
    DateTime PeriodEnd);

public record UsageLimitsResponse(Dictionary<string, int> Limits, UserPlanType PlanType);

// ==================== 优惠券管理 ====================

public record CouponResponse(Guid Id, string Code, string Type, decimal Value, string Currency, DateTime? ExpiresAt, bool IsUsed, DateTime? UsedAt);

public record RedeemCouponRequest(string Code);

public record ApplyCouponRequest(Guid SubscriptionId);

// ==================== 账单设置 ====================

public record BillingSettingsResponse(string? CompanyName, string? TaxId, BillingAddressResponse? BillingAddress, bool AutoRenew, string? PreferredCurrency);

public record BillingAddressResponse(string Street, string City, string State, string PostalCode, string Country);

public record UpdateBillingSettingsRequest(
    string? CompanyName,
    string? TaxId,
    BillingAddressResponse? BillingAddress,
    bool? AutoRenew,
    string? PreferredCurrency);