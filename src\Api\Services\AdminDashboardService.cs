using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AdminDashboardService
{
    public async Task<ServiceResult<AdminDashboardResponse>> GetDashboardDataAsync()
    {
        await Task.Delay(1);
        return ServiceResult<AdminDashboardResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<AdminKeyStatsResponse>> GetKeyStatsAsync()
    {
        await Task.Delay(1);
        return ServiceResult<AdminKeyStatsResponse>.Failure("功能暂未实现");
    }
}