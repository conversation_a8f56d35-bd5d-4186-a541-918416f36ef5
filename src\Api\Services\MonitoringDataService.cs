using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class MonitoringDataService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<MonitoringDataService> _logger;
    private readonly MonitoringService _monitoringService;

    public MonitoringDataService(AppDbContext dbContext, MonitoringService monitoringService, ILogger<MonitoringDataService> logger)
    {
        _dbContext = dbContext;
        _monitoringService = monitoringService;
        _logger = logger;
    }

    public async Task<ServiceResult<DashboardDataResponse>> GetDashboardDataAsync()
    {
        try
        {
            var systemOverviewTask = _monitoringService.GetSystemOverviewAsync();
            var workersOverviewTask = _monitoringService.GetWorkersOverviewAsync();
            var alertSummaryTask = _monitoringService.GetAlertSummaryAsync();

            await Task.WhenAll(systemOverviewTask, workersOverviewTask, alertSummaryTask);

            var systemOverview = systemOverviewTask.Result;
            var workersOverview = workersOverviewTask.Result;
            var alertSummary = alertSummaryTask.Result;

            if (!systemOverview.IsSuccess || !workersOverview.IsSuccess || !alertSummary.IsSuccess)
                return ServiceResult<DashboardDataResponse>.Failure("获取监控面板数据失败", "DASHBOARD_ERROR");

            var dashboardData = new DashboardDataResponse(systemOverview.Data!, workersOverview.Data!, alertSummary.Data!, DateTime.UtcNow);

            return ServiceResult<DashboardDataResponse>.Success(dashboardData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取监控面板数据时发生错误");
            return ServiceResult<DashboardDataResponse>.Failure("获取监控面板数据失败", "DASHBOARD_ERROR");
        }
    }

    public async Task<ServiceResult<PagedResponse<WorkerAlertResponse>>> GetAlertsAsync(int page = 1, int pageSize = 20)
    {
        try
        {
            var totalCount = await _dbContext.WorkerAlerts.CountAsync();

            var alerts = await _dbContext.WorkerAlerts.Include(a => a.Worker).OrderByDescending(a => a.CreatedAt).Skip((page - 1) * pageSize).Take(pageSize)
                .Select(a => new WorkerAlertResponse(a.Id, a.WorkerId, a.Worker.Name, a.AlertType, a.AlertLevel, a.Title, a.Message, a.IsResolved, a.CreatedAt,
                    a.ResolvedAt)).ToListAsync();

            var pagedResponse = new PagedResponse<WorkerAlertResponse>(alerts, totalCount, page, pageSize);

            return ServiceResult<PagedResponse<WorkerAlertResponse>>.Success(pagedResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取告警列表时发生错误");
            return ServiceResult<PagedResponse<WorkerAlertResponse>>.Failure("获取告警列表失败", "ALERTS_ERROR");
        }
    }

    public async Task<ServiceResult<List<WorkerAlertResponse>>> GetNodeAlertsAsync(Guid nodeId)
    {
        try
        {
            var alerts = await _dbContext.WorkerAlerts.Where(a => a.WorkerId == nodeId).Include(a => a.Worker).OrderByDescending(a => a.CreatedAt)
                .Take(50) // 限制返回最近50条
                .Select(a => new WorkerAlertResponse(a.Id, a.WorkerId, a.Worker.Name, a.AlertType, a.AlertLevel, a.Title, a.Message, a.IsResolved, a.CreatedAt,
                    a.ResolvedAt)).ToListAsync();

            return ServiceResult<List<WorkerAlertResponse>>.Success(alerts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取节点 {NodeId} 告警时发生错误", nodeId);
            return ServiceResult<List<WorkerAlertResponse>>.Failure("获取节点告警失败", "NODE_ALERTS_ERROR");
        }
    }

    public async Task<ServiceResult> ResolveAlertAsync(Guid alertId)
    {
        try
        {
            var alert = await _dbContext.WorkerAlerts.FindAsync(alertId);
            if (alert == null) return ServiceResult.Failure("告警不存在", "ALERT_NOT_FOUND");

            if (alert.IsResolved) return ServiceResult.Failure("告警已经被解决", "ALERT_ALREADY_RESOLVED");

            alert.IsResolved = true;
            alert.ResolvedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("告警 {AlertId} 已被解决", alertId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "解决告警 {AlertId} 时发生错误", alertId);
            return ServiceResult.Failure("解决告警失败", "RESOLVE_ALERT_ERROR");
        }
    }
}