namespace Shared.Common;

public class ServiceResult
{
    protected ServiceResult(bool isSuccess, string? errorMessage = null, string? errorCode = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        ErrorCode = errorCode;
    }

    public bool IsSuccess { get; init; }
    public string? ErrorMessage { get; init; }
    public string? ErrorCode { get; init; }

    public static ServiceResult Success()
    {
        return new ServiceResult(true);
    }

    public static ServiceResult Failure(string errorCode, string? errorMessage = null)
    {
        var message = errorMessage ?? ErrorCodes.GetDescription(errorCode);
        return new ServiceResult(false, message, errorCode);
    }
}

public class ServiceResult<T> : ServiceResult
{
    private ServiceResult(bool isSuccess, T? data = default, string? errorMessage = null, string? errorCode = null) : base(isSuccess, errorMessage, errorCode)
    {
        Data = data;
    }

    public T? Data { get; init; }

    public static ServiceResult<T> Success(T data)
    {
        ArgumentNullException.ThrowIfNull(data);
        return new ServiceResult<T>(true, data);
    }

    public new static ServiceResult<T> Failure(string errorCode, string? errorMessage = null)
    {
        var message = errorMessage ?? ErrorCodes.GetDescription(errorCode);
        return new ServiceResult<T>(false, default, message, errorCode);
    }
}