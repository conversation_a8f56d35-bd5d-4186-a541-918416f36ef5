using Api.Data;
using Api.Extensions;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class TaskQueryService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<TaskQueryService> _logger;

    public TaskQueryService(AppDbContext dbContext, ILogger<TaskQueryService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<ServiceResult<TaskListResponse>> GetUserTasksWithFilterAsync(Guid userId, int page = 1, int pageSize = 20,
        WorkerTaskStatus? status = null, WorkerTaskType? type = null)
    {
        try
        {
            var skip = (page - 1) * pageSize;

            var query = _dbContext.WorkerTasks.Where(t => t.UserId == userId && t.BatchTaskId == null);

            if (status.HasValue)
                query = query.Where(t => t.Status == status.Value);

            if (type.HasValue)
                query = query.Where(t => t.TaskType == type.Value);

            query = query.OrderByDescending(t => t.CreatedAt);

            var totalCount = await query.CountAsync();
            var tasks = await query.Skip(skip).Take(pageSize).ToListAsync();

            var taskResponses = tasks.Select(t => t.ToResponse()).ToList();

            var response = new TaskListResponse(taskResponses, new List<BatchTaskResponse>(), totalCount, page, pageSize);

            return ServiceResult<TaskListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户 {UserId} 任务列表时发生错误", userId);
            return ServiceResult<TaskListResponse>.Failure("获取任务列表失败");
        }
    }

    public async Task<ServiceResult<BatchTaskListResponse>> GetUserBatchTasksAsync(Guid userId, int page = 1, int pageSize = 20, BatchTaskStatus? status = null)
    {
        try
        {
            var skip = (page - 1) * pageSize;

            var query = _dbContext.BatchTasks.Where(bt => bt.UserId == userId);

            if (status.HasValue)
                query = query.Where(bt => bt.Status == status.Value);

            query = query.OrderByDescending(bt => bt.CreatedAt);

            var totalCount = await query.CountAsync();
            var batchTasks = await query.Skip(skip).Take(pageSize).Include(bt => bt.WorkerTasks).ToListAsync();

            var batchTaskResponses = batchTasks.Select(bt => bt.ToResponse()).ToList();

            var response = new BatchTaskListResponse(batchTaskResponses, totalCount, page, pageSize);

            return ServiceResult<BatchTaskListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户 {UserId} 批量任务列表时发生错误", userId);
            return ServiceResult<BatchTaskListResponse>.Failure("获取批量任务列表失败");
        }
    }
}