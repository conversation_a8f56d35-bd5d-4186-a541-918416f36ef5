using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class SystemConfigService
{
    public async Task<ServiceResult<SystemSettingsResponse>> GetAllSettingsAsync()
    {
        await Task.Delay(1);
        return ServiceResult<SystemSettingsResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> UpdateSettingsAsync(UpdateSystemSettingsRequest request)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<SettingValueResponse>> GetSettingAsync(string key)
    {
        await Task.Delay(1);
        return ServiceResult<SettingValueResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> UpdateSettingAsync(string key, UpdateSettingRequest request)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }
}