using Api.Extensions;
using Api.Services;
using Shared.DTOs;

namespace Api.Endpoints;

public static class NotificationEndpoints
{
    public static void MapNotificationEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/notifications").WithTags("Notifications").WithValidation();

        // ==================== 用户通知端点 ====================

        group.MapGet("/", async Task<IResult> (NotificationService notificationService, HttpContext context, int page = 1, int pageSize = 20,
            bool? isRead = null, string? type = null) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.GetUserNotificationsAsync(currentUser.UserId, page, pageSize, isRead, type);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户通知列表");

        group.MapGet("/{notificationId}", async Task<IResult> (Guid notificationId, NotificationService notificationService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.GetNotificationDetailAsync(notificationId, currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取通知详情");

        group.MapPost("/{notificationId}/read", async Task<IResult> (Guid notificationId, NotificationService notificationService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.MarkAsReadAsync(notificationId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "通知已标记为已读");
        }).WithSummary("标记通知为已读");

        group.MapPost("/read-all", async Task<IResult> (NotificationService notificationService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.MarkAllAsReadAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "所有通知已标记为已读");
        }).WithSummary("标记所有通知为已读");

        group.MapPost("/{notificationId}/delete", async Task<IResult> (Guid notificationId, NotificationService notificationService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.DeleteNotificationAsync(notificationId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "通知已删除");
        }).WithSummary("删除通知");

        group.MapPost("/delete-all", async Task<IResult> (NotificationService notificationService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.DeleteAllNotificationsAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "所有通知已删除");
        }).WithSummary("删除所有通知");

        // ==================== 通知统计端点 ====================

        group.MapGet("/count", async Task<IResult> (NotificationService notificationService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.GetNotificationCountAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取通知数量统计");

        group.MapGet("/unread-count", async Task<IResult> (NotificationService notificationService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await notificationService.GetUnreadCountAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取未读通知数量");

        // ==================== 通知设置端点 ====================

        group.MapGet("/settings", async Task<IResult> (NotificationSettingsService settingsService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await settingsService.GetNotificationSettingsAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取通知设置");

        group.MapPost("/settings",
            async Task<IResult> (UpdateNotificationSettingsRequest request, NotificationSettingsService settingsService, HttpContext context) =>
            {
                var currentUser = context.GetCurrentUser();
                if (currentUser == null) return ResultExtensions.ApiUnauthorized();
                var result = await settingsService.UpdateNotificationSettingsAsync(currentUser.UserId, request);
                return ResultExtensions.ToHttpResultOrOk(result, "通知设置已更新");
            }).WithSummary("更新通知设置");

        group.MapPost("/settings/reset", async Task<IResult> (NotificationSettingsService settingsService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await settingsService.ResetNotificationSettingsAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "通知设置已重置");
        }).WithSummary("重置通知设置");

        // ==================== 系统公告端点 ====================

        group.MapGet("/announcements", async Task<IResult> (AnnouncementService announcementService, int page = 1, int pageSize = 10, bool? isActive = true) =>
        {
            var result = await announcementService.GetAnnouncementsAsync(page, pageSize, isActive);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统公告");

        group.MapGet("/announcements/{announcementId}", async Task<IResult> (Guid announcementId, AnnouncementService announcementService) =>
        {
            var result = await announcementService.GetAnnouncementDetailAsync(announcementId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取公告详情");

        group.MapPost("/announcements/{announcementId}/read",
            async Task<IResult> (Guid announcementId, AnnouncementService announcementService, HttpContext context) =>
            {
                var currentUser = context.GetCurrentUser();
                if (currentUser == null) return ResultExtensions.ApiUnauthorized();
                var result = await announcementService.MarkAnnouncementAsReadAsync(announcementId, currentUser.UserId);
                return ResultExtensions.ToHttpResultOrOk(result, "公告已标记为已读");
            }).WithSummary("标记公告为已读");

        // ==================== 管理员通知管理端点 ====================

        var adminGroup = app.MapGroup("/api/admin/notifications").WithTags("Admin Notifications").WithValidation();

        adminGroup.MapPost("/broadcast", async Task<IResult> (BroadcastNotificationRequest request, AdminNotificationService adminNotificationService) =>
        {
            var result = await adminNotificationService.BroadcastNotificationAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("发送广播通知");

        adminGroup.MapPost("/send", async Task<IResult> (SendNotificationRequest request, AdminNotificationService adminNotificationService) =>
        {
            var result = await adminNotificationService.SendNotificationAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("发送通知给指定用户");

        adminGroup.MapPost("/announcements", async Task<IResult> (CreateAnnouncementRequest request, AdminNotificationService adminNotificationService) =>
        {
            var result = await adminNotificationService.CreateAnnouncementAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建系统公告");

        adminGroup.MapPost("/announcements/{announcementId}/update",
            async Task<IResult> (Guid announcementId, UpdateAnnouncementRequest request, AdminNotificationService adminNotificationService) =>
            {
                var result = await adminNotificationService.UpdateAnnouncementAsync(announcementId, request);
                return ResultExtensions.ToHttpResultOrOk(result, "公告已更新");
            }).WithSummary("更新系统公告");

        adminGroup.MapPost("/announcements/{announcementId}/delete",
            async Task<IResult> (Guid announcementId, AdminNotificationService adminNotificationService) =>
            {
                var result = await adminNotificationService.DeleteAnnouncementAsync(announcementId);
                return ResultExtensions.ToHttpResultOrOk(result, "公告已删除");
            }).WithSummary("删除系统公告");

        adminGroup.MapGet("/stats", async Task<IResult> (AdminNotificationService adminNotificationService) =>
        {
            var result = await adminNotificationService.GetNotificationStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取通知统计");

        // ==================== 通知模板管理端点 ====================

        adminGroup.MapGet("/templates",
            async Task<IResult> (NotificationTemplateService templateService, int page = 1, int pageSize = 20, string? type = null) =>
            {
                var result = await templateService.GetTemplatesAsync(page, pageSize, type);
                return ResultExtensions.ToHttpResult(result);
            }).WithSummary("获取通知模板列表");

        adminGroup.MapPost("/templates", async Task<IResult> (CreateNotificationTemplateRequest request, NotificationTemplateService templateService) =>
        {
            var result = await templateService.CreateTemplateAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建通知模板");

        adminGroup.MapPost("/templates/{templateId}/update",
            async Task<IResult> (Guid templateId, UpdateNotificationTemplateRequest request, NotificationTemplateService templateService) =>
            {
                var result = await templateService.UpdateTemplateAsync(templateId, request);
                return ResultExtensions.ToHttpResultOrOk(result, "模板已更新");
            }).WithSummary("更新通知模板");

        adminGroup.MapPost("/templates/{templateId}/delete", async Task<IResult> (Guid templateId, NotificationTemplateService templateService) =>
        {
            var result = await templateService.DeleteTemplateAsync(templateId);
            return ResultExtensions.ToHttpResultOrOk(result, "模板已删除");
        }).WithSummary("删除通知模板");
    }
}