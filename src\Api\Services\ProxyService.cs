using System.Collections.Concurrent;
using System.Net;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class ProxyService(AppDbContext dbContext, ILogger<ProxyService> logger)
{
    private readonly TimeSpan _minInterval = TimeSpan.FromSeconds(5);
    private readonly ConcurrentDictionary<Guid, DateTime> _proxyLastUsed = new();

    public async Task<ServiceResult<int>> InitializeProxyPoolAsync()
    {
        try
        {
            var existingCount = await dbContext.Proxies.CountAsync();
            if (existingCount > 0)
            {
                logger.LogInformation("代理池已初始化，包含 {Count} 个代理", existingCount);
                return ServiceResult<int>.Success(existingCount);
            }

            var proxies = new List<Proxy>();
            for (var i = 1; i <= 100; i++)
            {
                var proxy = new Proxy
                {
                    Id = Guid.NewGuid(),
                    Host = "p.webshare.io",
                    Port = 80,
                    Username = $"vakzyshu-{i}",
                    Password = "xx9skjnzudof",
                    ProxyType = ProxyType.Http,
                    Status = ProxyStatus.Active,
                    HealthStatus = ProxyHealthStatus.Healthy,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                proxies.Add(proxy);
            }

            dbContext.Proxies.AddRange(proxies);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("已初始化代理池，包含 {Count} 个代理", proxies.Count);
            return ServiceResult<int>.Success(proxies.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "初始化代理池时发生错误");
            return ServiceResult<int>.Failure("初始化代理池失败");
        }
    }

    public async Task<ServiceResult<ProxyDetailResponse>> AddProxyAsync(AddProxyRequest request)
    {
        try
        {
            var existingProxy =
                await dbContext.Proxies.FirstOrDefaultAsync(p => p.Host == request.Host && p.Port == request.Port && p.Username == request.Username);

            if (existingProxy != null)
                return ServiceResult<ProxyDetailResponse>.Failure("代理已存在", ErrorCodes.CONFLICT);

            var proxy = new Proxy
            {
                Id = Guid.NewGuid(),
                Host = request.Host,
                Port = request.Port,
                Username = request.Username ?? "",
                Password = request.Password ?? "",
                ProxyType = request.Type,
                Status = ProxyStatus.Active,
                HealthStatus = ProxyHealthStatus.Unknown,
                Notes = request.Notes,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            dbContext.Proxies.Add(proxy);
            await dbContext.SaveChangesAsync();

            var response = MapToProxyDetailResponse(proxy);
            logger.LogInformation("已添加代理: {Host}:{Port}", proxy.Host, proxy.Port);

            return ServiceResult<ProxyDetailResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "添加代理时发生错误");
            return ServiceResult<ProxyDetailResponse>.Failure("添加代理失败");
        }
    }

    public async Task<ServiceResult<List<ProxyDetailResponse>>> BatchAddProxiesAsync(BatchAddProxyRequest request)
    {
        try
        {
            var responses = new List<ProxyDetailResponse>();
            var addedCount = 0;

            foreach (var proxyRequest in request.Proxies)
            {
                var existingProxy = await dbContext.Proxies.FirstOrDefaultAsync(p =>
                    p.Host == proxyRequest.Host && p.Port == proxyRequest.Port && p.Username == proxyRequest.Username);

                if (existingProxy != null)
                {
                    logger.LogWarning("代理已存在，跳过: {Host}:{Port}", proxyRequest.Host, proxyRequest.Port);
                    continue;
                }

                var proxy = new Proxy
                {
                    Id = Guid.NewGuid(),
                    Host = proxyRequest.Host,
                    Port = proxyRequest.Port,
                    Username = proxyRequest.Username ?? "",
                    Password = proxyRequest.Password ?? "",
                    ProxyType = proxyRequest.Type,
                    Status = ProxyStatus.Active,
                    HealthStatus = ProxyHealthStatus.Unknown,
                    Notes = proxyRequest.Notes,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                dbContext.Proxies.Add(proxy);
                responses.Add(MapToProxyDetailResponse(proxy));
                addedCount++;
            }

            await dbContext.SaveChangesAsync();
            logger.LogInformation("批量添加代理完成，成功添加 {Count} 个代理", addedCount);

            return ServiceResult<List<ProxyDetailResponse>>.Success(responses);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "批量添加代理时发生错误");
            return ServiceResult<List<ProxyDetailResponse>>.Failure("批量添加代理失败");
        }
    }

    public async Task<ServiceResult<ProxyDetailResponse>> UpdateProxyAsync(UpdateProxyRequest request)
    {
        try
        {
            var proxy = await dbContext.Proxies.FirstOrDefaultAsync(p => p.Id == request.ProxyId);
            if (proxy == null)
                return ServiceResult<ProxyDetailResponse>.Failure("代理不存在", ErrorCodes.NOT_FOUND);

            if (request.Status.HasValue)
                proxy.Status = request.Status.Value;

            if (!string.IsNullOrEmpty(request.Notes))
                proxy.Notes = request.Notes;

            proxy.UpdatedAt = DateTime.UtcNow;
            await dbContext.SaveChangesAsync();

            var response = MapToProxyDetailResponse(proxy);
            logger.LogInformation("已更新代理: {ProxyId}", request.ProxyId);

            return ServiceResult<ProxyDetailResponse>.Success(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "更新代理时发生错误");
            return ServiceResult<ProxyDetailResponse>.Failure("更新代理失败");
        }
    }

    public async Task<ServiceResult> DeleteProxyAsync(DeleteProxyRequest request)
    {
        try
        {
            var proxy = await dbContext.Proxies.FirstOrDefaultAsync(p => p.Id == request.ProxyId);
            if (proxy == null)
                return ServiceResult.Failure("代理不存在", ErrorCodes.NOT_FOUND);

            dbContext.Proxies.Remove(proxy);
            await dbContext.SaveChangesAsync();

            logger.LogInformation("已删除代理: {ProxyId}", request.ProxyId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "删除代理时发生错误");
            return ServiceResult.Failure("删除代理失败");
        }
    }

    public async Task<ServiceResult<int>> HealthCheckAllProxiesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var proxies = await dbContext.Proxies.Where(p => p.Status == ProxyStatus.Active).ToListAsync(cancellationToken);

            if (proxies.Count == 0)
            {
                logger.LogInformation("没有活跃的代理需要检查");
                return ServiceResult<int>.Success(0);
            }

            var healthyCount = 0;
            const int batchSize = 10;

            for (var i = 0; i < proxies.Count; i += batchSize)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batch = proxies.Skip(i).Take(batchSize);

                var tasks = batch.Select(async proxy =>
                {
                    var isHealthy = await CheckProxyHealthAsync(proxy, cancellationToken);
                    return new { Proxy = proxy, IsHealthy = isHealthy };
                });

                var results = await Task.WhenAll(tasks);

                foreach (var result in results)
                {
                    result.Proxy.HealthStatus = result.IsHealthy ? ProxyHealthStatus.Healthy : ProxyHealthStatus.Unhealthy;
                    result.Proxy.LastHealthCheckAt = DateTime.UtcNow;

                    if (result.IsHealthy)
                    {
                        healthyCount++;
                        result.Proxy.FailureCount = 0;
                        result.Proxy.SuccessCount++;
                    }
                    else
                    {
                        result.Proxy.FailureCount++;
                        if (result.Proxy.FailureCount >= 5)
                        {
                            result.Proxy.Status = ProxyStatus.Inactive;
                            logger.LogWarning("代理 {ProxyId} 因连续失败被标记为不活跃", result.Proxy.Id);
                        }
                    }
                }

                logger.LogDebug("完成批次 {BatchNumber}/{TotalBatches} 的健康检查", i / batchSize + 1, (proxies.Count + batchSize - 1) / batchSize);
            }

            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("健康检查完成。{HealthyCount}/{TotalCount} 个代理健康", healthyCount, proxies.Count);

            return ServiceResult<int>.Success(healthyCount);
        }
        catch (OperationCanceledException)
        {
            logger.LogWarning("代理健康检查被取消");
            return ServiceResult<int>.Failure("代理健康检查被取消");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "代理健康检查时发生错误");
            return ServiceResult<int>.Failure("代理健康检查失败");
        }
    }

    public async Task<ServiceResult<ProxyInfo?>> GetAvailableProxyAsync()
    {
        try
        {
            var availableProxies = await dbContext.Proxies.Where(p => p.Status == ProxyStatus.Active && p.HealthStatus == ProxyHealthStatus.Healthy)
                .OrderBy(p => p.UsageCount).ThenBy(p => p.LastUsedAt).ToListAsync();

            if (availableProxies.Count == 0)
            {
                logger.LogWarning("未找到可用代理");
                return ServiceResult<ProxyInfo?>.Success(null);
            }

            var now = DateTime.UtcNow;

            foreach (var proxy in availableProxies)
                if (!_proxyLastUsed.TryGetValue(proxy.Id, out var lastUsed) || now - lastUsed >= _minInterval)
                {
                    _proxyLastUsed[proxy.Id] = now;
                    proxy.LastUsedAt = now;
                    proxy.UsageCount++;
                    await dbContext.SaveChangesAsync();

                    var proxyInfo = new ProxyInfo(proxy.Host, proxy.Port, proxy.Username, proxy.Password, proxy.ProxyType);

                    logger.LogDebug("选择代理: {Host}:{Port} (使用次数: {UsageCount})", proxy.Host, proxy.Port, proxy.UsageCount);

                    return ServiceResult<ProxyInfo?>.Success(proxyInfo);
                }

            var earliestProxy = availableProxies.OrderBy(p => _proxyLastUsed.GetValueOrDefault(p.Id, DateTime.MinValue)).First();
            _proxyLastUsed[earliestProxy.Id] = now;
            earliestProxy.LastUsedAt = now;
            earliestProxy.UsageCount++;
            await dbContext.SaveChangesAsync();

            var fallbackProxyInfo = new ProxyInfo(earliestProxy.Host, earliestProxy.Port, earliestProxy.Username, earliestProxy.Password,
                earliestProxy.ProxyType);

            logger.LogDebug("使用备用代理: {Host}:{Port} (使用次数: {UsageCount})", earliestProxy.Host, earliestProxy.Port, earliestProxy.UsageCount);

            return ServiceResult<ProxyInfo?>.Success(fallbackProxyInfo);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取可用代理信息时发生错误");
            return ServiceResult<ProxyInfo?>.Failure("获取代理信息失败");
        }
    }

    public async Task<ServiceResult<ProxyStats>> GetProxyStatsAsync()
    {
        try
        {
            var proxies = await dbContext.Proxies.ToListAsync();

            var totalProxies = proxies.Count;
            var activeProxies = proxies.Count(p => p.Status == ProxyStatus.Active);
            var healthyProxies = proxies.Count(p => p.Status == ProxyStatus.Active && p.HealthStatus == ProxyHealthStatus.Healthy);
            var unhealthyProxies = proxies.Count(p => p.Status == ProxyStatus.Active && p.HealthStatus == ProxyHealthStatus.Unhealthy);
            var healthRate = activeProxies > 0 ? (double)healthyProxies / activeProxies * 100 : 0;
            var lastHealthCheck = proxies.Where(p => p.LastHealthCheckAt.HasValue).Max(p => p.LastHealthCheckAt) ?? DateTime.MinValue;

            var stats = new ProxyStats(totalProxies, activeProxies, healthyProxies, unhealthyProxies, healthRate, lastHealthCheck);

            return ServiceResult<ProxyStats>.Success(stats);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "获取代理统计时发生错误");
            return ServiceResult<ProxyStats>.Failure("获取代理统计失败");
        }
    }

    private async Task<bool> CheckProxyHealthAsync(Proxy proxy, CancellationToken cancellationToken = default)
    {
        try
        {
            using var handler = new HttpClientHandler();

            if (proxy.ProxyType is ProxyType.Http or ProxyType.Https)
            {
                var proxyUrl = FormatProxyUrl(proxy);
                handler.Proxy = new WebProxy(proxyUrl);
            }

            using var proxyClient = new HttpClient(handler);
            proxyClient.Timeout = TimeSpan.FromSeconds(10);

            var testUrls = new[]
            {
                "https://www.youtube.com",
                "https://httpbin.org/ip",
                "https://api.github.com"
            };

            foreach (var testUrl in testUrls)
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var startTime = DateTime.UtcNow;
                    var response = await proxyClient.GetAsync(testUrl, cancellationToken);
                    if (response.IsSuccessStatusCode)
                    {
                        var responseTime = DateTime.UtcNow - startTime;
                        proxy.ResponseTimeMs = (int)responseTime.TotalMilliseconds;
                        proxy.ErrorMessage = null;
                        return true;
                    }
                }
                catch (HttpRequestException httpEx)
                {
                    logger.LogDebug("代理 {ProxyId} HTTP请求失败 {Url}: {Error}", proxy.Id, testUrl, httpEx.Message);
                }
                catch (TaskCanceledException timeoutEx) when (!cancellationToken.IsCancellationRequested)
                {
                    logger.LogDebug("代理 {ProxyId} 请求超时 {Url}: {Error}", proxy.Id, testUrl, timeoutEx.Message);
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    logger.LogDebug("代理 {ProxyId} 测试异常 {Url}: {Error}", proxy.Id, testUrl, ex.Message);
                }

            proxy.ErrorMessage = "所有测试URL都失败";
            return false;
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception ex)
        {
            logger.LogDebug("代理 {ProxyId} 健康检查失败: {Error}", proxy.Id, ex.Message);
            proxy.ErrorMessage = ex.Message;
            return false;
        }
    }


    private static string FormatProxyUrl(Proxy proxy)
    {
        var protocol = GetProxyProtocol(proxy.ProxyType);
        return $"{protocol}://{proxy.Username}:{proxy.Password}@{proxy.Host}:{proxy.Port}";
    }

    private static string GetProxyProtocol(ProxyType proxyType)
    {
        return proxyType switch
        {
            ProxyType.Http => "http",
            ProxyType.Https => "https",
            ProxyType.Socks5 => "socks5",
            _ => "http"
        };
    }

    private static ProxyDetailResponse MapToProxyDetailResponse(Proxy proxy)
    {
        return new ProxyDetailResponse(proxy.Id, proxy.Host, proxy.Port, proxy.Username, proxy.ProxyType, proxy.Status, proxy.HealthStatus, proxy.LastUsedAt,
            proxy.LastHealthCheckAt, proxy.ResponseTimeMs, proxy.FailureCount, proxy.SuccessCount, proxy.UsageCount, proxy.ErrorMessage, proxy.CreatedAt,
            proxy.UpdatedAt, proxy.Notes);
    }
}