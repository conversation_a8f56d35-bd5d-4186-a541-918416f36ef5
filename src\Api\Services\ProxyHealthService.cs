using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class ProxyHealthService
{
    public async Task<ServiceResult<ProxyHealthCheckResponse>> CheckAllProxiesAsync()
    {
        await Task.Delay(1);
        return ServiceResult<ProxyHealthCheckResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<SingleProxyHealthResponse>> CheckProxyHealthAsync(Guid proxyId)
    {
        await Task.Delay(1);
        return ServiceResult<SingleProxyHealthResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<ProxyHealthSummaryResponse>> GetHealthSummaryAsync()
    {
        await Task.Delay(1);
        return ServiceResult<ProxyHealthSummaryResponse>.Failure("功能暂未实现");
    }
}