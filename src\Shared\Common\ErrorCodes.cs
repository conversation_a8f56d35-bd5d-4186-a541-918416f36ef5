namespace Shared.Common;

public static class ErrorCodes
{
    public const string VALIDATION_ERROR = "VALIDATION_ERROR";
    public const string INTERNAL_ERROR = "INTERNAL_ERROR";
    public const string UNAUTHORIZED = "UNAUTHORIZED";
    public const string FORBIDDEN = "FORBIDDEN";
    public const string NOT_FOUND = "NOT_FOUND";
    public const string CONFLICT = "CONFLICT";
    public const string TOO_MANY_REQUESTS = "TOO_MANY_REQUESTS";
    public const string INVALID_ARGUMENT = "INVALID_ARGUMENT";
    public const string INVALID_OPERATION = "INVALID_OPERATION";
    public const string NOT_SUPPORTED = "NOT_SUPPORTED";
    public const string TIMEOUT = "TIMEOUT";
    public const string OPERATION_CANCELLED = "OPERATION_CANCELLED";

    public const string USER_NOT_FOUND = "USER_NOT_FOUND";
    public const string USER_EMAIL_EXISTS = "USER_EMAIL_EXISTS";
    public const string USER_DISABLED = "USER_DISABLED";
    public const string USER_QUOTA_EXCEEDED = "USER_QUOTA_EXCEEDED";
    public const string PLAN_QUOTA_EXCEEDED = "PLAN_QUOTA_EXCEEDED";

    public const string AUTH_TOKEN_INVALID = "AUTH_TOKEN_INVALID";
    public const string AUTH_TOKEN_EXPIRED = "AUTH_TOKEN_EXPIRED";
    public const string AUTH_CREDENTIALS_INVALID = "AUTH_CREDENTIALS_INVALID";

    public const string TASK_NOT_FOUND = "TASK_NOT_FOUND";
    public const string TASK_ALREADY_STARTED = "TASK_ALREADY_STARTED";
    public const string TASK_ALREADY_COMPLETED = "TASK_ALREADY_COMPLETED";
    public const string TASK_CANCELLED = "TASK_CANCELLED";
    public const string TASK_FAILED = "TASK_FAILED";
    public const string TASK_QUEUE_FULL = "TASK_QUEUE_FULL";
    public const string UNSUPPORTED_TASK_TYPE = "UNSUPPORTED_TASK_TYPE";
    public const string BATCH_TASK_NOT_FOUND = "BATCH_TASK_NOT_FOUND";

    public const string YOUTUBE_VIDEO_NOT_FOUND = "YOUTUBE_VIDEO_NOT_FOUND";
    public const string YOUTUBE_VIDEO_PRIVATE = "YOUTUBE_VIDEO_PRIVATE";
    public const string YOUTUBE_VIDEO_UNAVAILABLE = "YOUTUBE_VIDEO_UNAVAILABLE";
    public const string YOUTUBE_PLAYLIST_NOT_FOUND = "YOUTUBE_PLAYLIST_NOT_FOUND";
    public const string YOUTUBE_CHANNEL_NOT_FOUND = "YOUTUBE_CHANNEL_NOT_FOUND";
    public const string YOUTUBE_RATE_LIMITED = "YOUTUBE_RATE_LIMITED";
    public const string YOUTUBE_API_ERROR = "YOUTUBE_API_ERROR";
    public const string INVALID_VIDEO_ID = "INVALID_VIDEO_ID";
    public const string INVALID_PLAYLIST_ID = "INVALID_PLAYLIST_ID";
    public const string INVALID_CHANNEL_ID = "INVALID_CHANNEL_ID";
    public const string INVALID_URL = "INVALID_URL";
    public const string INVALID_URL_FORMAT = "INVALID_URL_FORMAT";

    public const string WORKER_NOT_FOUND = "WORKER_NOT_FOUND";
    public const string WORKER_UNAVAILABLE = "WORKER_UNAVAILABLE";
    public const string WORKER_OVERLOADED = "WORKER_OVERLOADED";
    public const string WORKER_PROCESSING_ERROR = "WORKER_PROCESSING_ERROR";

    public const string FILE_NOT_FOUND = "FILE_NOT_FOUND";
    public const string FILE_TOO_LARGE = "FILE_TOO_LARGE";
    public const string FILE_EXPIRED = "FILE_EXPIRED";
    public const string FILE_PROCESSING_ERROR = "FILE_PROCESSING_ERROR";

    public const string PROXY_NOT_AVAILABLE = "PROXY_NOT_AVAILABLE";
    public const string PROXY_CONNECTION_FAILED = "PROXY_CONNECTION_FAILED";
    public const string PROXY_RATE_LIMITED = "PROXY_RATE_LIMITED";

    public const string FILE_ACCESS_DENIED = "FILE_ACCESS_DENIED";
    public const string DIRECTORY_NOT_FOUND = "DIRECTORY_NOT_FOUND";
    public const string HTTP_ERROR = "HTTP_ERROR";
    public const string NETWORK_ERROR = "NETWORK_ERROR";
    public const string JSON_ERROR = "JSON_ERROR";
    public const string INSUFFICIENT_MEMORY = "INSUFFICIENT_MEMORY";

    public static int GetHttpStatusCode(string errorCode)
    {
        return errorCode switch
        {
            VALIDATION_ERROR or INVALID_ARGUMENT or INVALID_OPERATION or INVALID_VIDEO_ID or INVALID_PLAYLIST_ID or INVALID_CHANNEL_ID or INVALID_URL
                or INVALID_URL_FORMAT => 400,
            UNAUTHORIZED or AUTH_TOKEN_EXPIRED or AUTH_TOKEN_INVALID or AUTH_CREDENTIALS_INVALID => 401,
            FORBIDDEN or USER_DISABLED => 403,
            NOT_FOUND or USER_NOT_FOUND or TASK_NOT_FOUND or BATCH_TASK_NOT_FOUND or YOUTUBE_VIDEO_NOT_FOUND or YOUTUBE_PLAYLIST_NOT_FOUND
                or YOUTUBE_CHANNEL_NOT_FOUND or WORKER_NOT_FOUND or FILE_NOT_FOUND or DIRECTORY_NOT_FOUND => 404,
            CONFLICT or USER_EMAIL_EXISTS or TASK_ALREADY_STARTED or TASK_ALREADY_COMPLETED => 409,
            TOO_MANY_REQUESTS or YOUTUBE_RATE_LIMITED or PROXY_RATE_LIMITED or USER_QUOTA_EXCEEDED or PLAN_QUOTA_EXCEEDED => 429,
            NOT_SUPPORTED => 501,
            TIMEOUT => 408,
            OPERATION_CANCELLED => 499,
            _ => 500
        };
    }

    public static string GetDescription(string errorCode)
    {
        return errorCode switch
        {
            VALIDATION_ERROR => "输入验证失败",
            INTERNAL_ERROR => "服务器内部错误",
            UNAUTHORIZED => "未授权访问",
            FORBIDDEN => "禁止访问",
            NOT_FOUND => "资源未找到",
            CONFLICT => "资源冲突",
            TOO_MANY_REQUESTS => "请求过于频繁",
            INVALID_ARGUMENT => "参数无效",
            INVALID_OPERATION => "操作无效",
            NOT_SUPPORTED => "不支持的操作",
            TIMEOUT => "操作超时",
            OPERATION_CANCELLED => "操作已取消",

            USER_NOT_FOUND => "用户不存在",
            USER_EMAIL_EXISTS => "邮箱已存在",
            USER_DISABLED => "用户已被禁用",
            USER_QUOTA_EXCEEDED => "用户配额已超限",
            PLAN_QUOTA_EXCEEDED => "套餐配额已超限",

            AUTH_TOKEN_INVALID => "认证令牌无效",
            AUTH_TOKEN_EXPIRED => "认证令牌已过期",
            AUTH_CREDENTIALS_INVALID => "用户名或密码错误",

            TASK_NOT_FOUND => "任务不存在",
            TASK_ALREADY_STARTED => "任务已开始",
            TASK_ALREADY_COMPLETED => "任务已完成",
            TASK_CANCELLED => "任务已取消",
            TASK_FAILED => "任务执行失败",
            TASK_QUEUE_FULL => "任务队列已满",
            UNSUPPORTED_TASK_TYPE => "不支持的任务类型",
            BATCH_TASK_NOT_FOUND => "批量任务不存在",

            YOUTUBE_VIDEO_NOT_FOUND => "视频不存在",
            YOUTUBE_VIDEO_PRIVATE => "视频为私有",
            YOUTUBE_VIDEO_UNAVAILABLE => "视频不可用",
            YOUTUBE_PLAYLIST_NOT_FOUND => "播放列表不存在",
            YOUTUBE_CHANNEL_NOT_FOUND => "频道不存在",
            YOUTUBE_RATE_LIMITED => "YouTube API限流",
            YOUTUBE_API_ERROR => "YouTube API错误",
            INVALID_VIDEO_ID => "无效的视频ID",
            INVALID_PLAYLIST_ID => "无效的播放列表ID",
            INVALID_CHANNEL_ID => "无效的频道ID",
            INVALID_URL => "无效的URL",
            INVALID_URL_FORMAT => "无法识别的URL格式",

            WORKER_NOT_FOUND => "工作节点不存在",
            WORKER_UNAVAILABLE => "工作节点不可用",
            WORKER_OVERLOADED => "工作节点过载",
            WORKER_PROCESSING_ERROR => "工作节点处理错误",

            FILE_NOT_FOUND => "文件不存在",
            FILE_TOO_LARGE => "文件过大",
            FILE_EXPIRED => "文件已过期",
            FILE_PROCESSING_ERROR => "文件处理错误",

            PROXY_NOT_AVAILABLE => "代理不可用",
            PROXY_CONNECTION_FAILED => "代理连接失败",
            PROXY_RATE_LIMITED => "代理限流",

            FILE_ACCESS_DENIED => "文件访问被拒绝",
            DIRECTORY_NOT_FOUND => "目录未找到",
            HTTP_ERROR => "HTTP请求错误",
            NETWORK_ERROR => "网络连接错误",
            JSON_ERROR => "JSON解析错误",
            INSUFFICIENT_MEMORY => "内存不足",

            _ => "未知错误"
        };
    }

    public static bool IsRetryableError(string errorCode)
    {
        return errorCode switch
        {
            TIMEOUT or NETWORK_ERROR or HTTP_ERROR or PROXY_CONNECTION_FAILED or YOUTUBE_RATE_LIMITED or PROXY_RATE_LIMITED or WORKER_UNAVAILABLE
                or WORKER_OVERLOADED or INSUFFICIENT_MEMORY or YOUTUBE_API_ERROR => true,
            _ => false
        };
    }

    public static ErrorSeverity GetErrorSeverity(string errorCode)
    {
        return errorCode switch
        {
            INTERNAL_ERROR or INSUFFICIENT_MEMORY or WORKER_PROCESSING_ERROR => ErrorSeverity.Critical,
            UNAUTHORIZED or FORBIDDEN or USER_DISABLED or AUTH_TOKEN_EXPIRED or AUTH_TOKEN_INVALID or AUTH_CREDENTIALS_INVALID
                or FILE_ACCESS_DENIED => ErrorSeverity.High,
            VALIDATION_ERROR or INVALID_ARGUMENT or INVALID_OPERATION or INVALID_VIDEO_ID or INVALID_PLAYLIST_ID or INVALID_CHANNEL_ID or INVALID_URL
                or INVALID_URL_FORMAT or TASK_FAILED or YOUTUBE_API_ERROR or FILE_PROCESSING_ERROR => ErrorSeverity.Medium,
            _ => ErrorSeverity.Low
        };
    }
}