using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class SystemMaintenanceService
{
    public async Task<ServiceResult<MaintenanceStatusResponse>> GetMaintenanceStatusAsync()
    {
        await Task.Delay(1);
        return ServiceResult<MaintenanceStatusResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> EnableMaintenanceModeAsync(EnableMaintenanceRequest request)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> DisableMaintenanceModeAsync()
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }
}