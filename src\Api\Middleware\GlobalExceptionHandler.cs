﻿using System.Text.Json;
using FluentValidation;
using Microsoft.AspNetCore.Diagnostics;
using Shared.Common;

namespace Api.Middleware;

public class GlobalExceptionHandler : IExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;

    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
    {
        _logger = logger;
    }

    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
    {
        _logger.LogError(exception, "发生未处理的异常");

        var response = exception switch
        {
            // 验证异常（由ValidationFilter抛出，统一在此处理）
            ValidationException validationEx => ApiResponse.ValidationError(validationEx.Errors.Select(e => new ValidationError(e.PropertyName, e.ErrorMessage))
                .ToList()),

            // 未授权访问异常
            UnauthorizedAccessException => ApiResponse.Error("未授权访问", ErrorCodes.UNAUTHORIZED),

            // 参数异常
            ArgumentException argEx => ApiResponse.Error(argEx.Message, ErrorCodes.INVALID_ARGUMENT),

            // 操作无效异常
            InvalidOperationException invalidOpEx => ApiResponse.Error(invalidOpEx.Message, ErrorCodes.INVALID_OPERATION),

            // 不支持的操作异常
            NotSupportedException notSupportedEx => ApiResponse.Error(notSupportedEx.Message, ErrorCodes.NOT_SUPPORTED),

            // 超时异常
            TimeoutException => ApiResponse.Error("操作超时", ErrorCodes.TIMEOUT),

            // 任务取消异常
            TaskCanceledException => ApiResponse.Error("操作被取消", ErrorCodes.OPERATION_CANCELLED),

            // 默认服务器内部错误
            _ => ApiResponse.Error("服务器内部错误", ErrorCodes.INTERNAL_ERROR)
        };

        var statusCode = exception switch
        {
            ValidationException => 400,
            UnauthorizedAccessException => 401,
            ArgumentException or ArgumentNullException => 400,
            InvalidOperationException => 400,
            NotSupportedException => 501,
            TimeoutException => 408,
            TaskCanceledException => 499, // Client Closed Request
            _ => 500
        };

        httpContext.Response.StatusCode = statusCode;
        httpContext.Response.ContentType = "application/json";

        var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await httpContext.Response.WriteAsync(json, cancellationToken);
        return true;
    }
}