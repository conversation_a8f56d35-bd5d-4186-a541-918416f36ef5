using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class MonitoringRealtimeService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<MonitoringRealtimeService> _logger;

    public MonitoringRealtimeService(AppDbContext dbContext, ILogger<MonitoringRealtimeService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<ServiceResult<RealtimeStatsResponse>> GetRealtimeStatsAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var last5Minutes = now.AddMinutes(-5);

            // 获取活跃工作节点数量
            var activeWorkers = await _dbContext.Workers.Where(w => w.Status != WorkerStatus.Offline && w.LastActiveAt >= last5Minutes).CountAsync();

            // 获取当前活跃任务数量
            var activeTasks = await _dbContext.WorkerTasks.Where(t => t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued)
                .CountAsync();

            // 获取待处理任务数量
            var pendingTasks = await _dbContext.WorkerTasks.Where(t => t.Status == WorkerTaskStatus.Pending).CountAsync();

            // 获取最近5分钟完成的任务数量
            var recentCompletedTasks = await _dbContext.WorkerTasks
                .Where(t => t.Status == WorkerTaskStatus.Completed && t.CompletedAt >= last5Minutes).CountAsync();

            // 获取最近5分钟失败的任务数量
            var recentFailedTasks = await _dbContext.WorkerTasks.Where(t => t.Status == WorkerTaskStatus.Failed && t.CompletedAt >= last5Minutes).CountAsync();

            // 获取未解决的告警数量
            var unresolvedAlerts = await _dbContext.WorkerAlerts.Where(a => !a.IsResolved).CountAsync();

            // 获取最新的系统资源使用情况
            var latestMetrics = await _dbContext.WorkerMetrics.Where(m => m.RecordedAt >= last5Minutes).GroupBy(m => 1).Select(g => new
            {
                AvgCpu = g.Average(m => m.CpuUsagePercent),
                AvgMemory = g.Average(m => m.MemoryUsagePercent),
                AvgDisk = g.Average(m => m.DiskUsagePercent),
                TotalNetworkReceived = g.Sum(m => m.NetworkReceivedGB),
                TotalNetworkSent = g.Sum(m => m.NetworkSentGB)
            }).FirstOrDefaultAsync();

            var stats = new RealtimeStatsResponse(activeWorkers, activeTasks, pendingTasks, recentCompletedTasks, recentFailedTasks, unresolvedAlerts,
                latestMetrics?.AvgCpu ?? 0, latestMetrics?.AvgMemory ?? 0, latestMetrics?.AvgDisk ?? 0, latestMetrics?.TotalNetworkReceived ?? 0,
                latestMetrics?.TotalNetworkSent ?? 0, DateTime.UtcNow);

            return ServiceResult<RealtimeStatsResponse>.Success(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实时统计时发生错误");
            return ServiceResult<RealtimeStatsResponse>.Failure("获取实时统计失败", "REALTIME_STATS_ERROR");
        }
    }
}