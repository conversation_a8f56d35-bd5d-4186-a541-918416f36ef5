后端开发规范 (ASP.NET Core Minimal API / C# 12 / EF Core)

1. 核心目标与通用原则
目标： 高性能、简洁、现代化 ASP.NET Core 9 Minimal API C# 应用。
代码： 健壮、可维护、安全，遵循微软最佳实践。
抽象： 优先具体类，必要时才用接口。
分层设计： 强制严格分层。实体（数据层/领域层），DTO（应用层/表示层）。严禁跨层直接引用不属于该层的数据类型。

2. 代码风格与 C# / .NET 使用
通用风格： 简洁、地道、高性能 C#。遵循最新 C# 编码约定。
命名约定： PascalCase (类/方法/公有), camelCase (局部/参数/私有), UPPER_SNAKE_CASE (const), PascalCase (static readonly)。DTO 名称清晰。
格式化： 标准 C# 格式。善用 ?.、??、$ 等语法糖。类型明显时优先 var。
语言特性 (C# 12+)： 充分利用新特性，包括 record (DTO 强制), 主构造函数, 集合字面量 ([] 强制初始化集合), required (必填字段), 模式匹配。
框架特性： 充分利用 ASP.NET Core 内置功能 (路由, DI, 配置, 日志, 认证/授权等)。
注释： 仅在必要时添加清晰、简洁的中文注释。优先行内注释。
文件结构： 按类型分组 (e.g., Endpoints, Data, Models, Services)。

3. 数据建模与访问
3.1 实体 (Entity) 规范
用途： 数据库表/持久化对象直接映射。
特性： 贫血模型，仅作数据容器。严禁业务逻辑或服务依赖。
内容： 包含数据库映射配置（Fluent API），数据属性，显式导航属性。
解耦： 严禁直接依赖 DTO、UI 或服务接口。
不可变性： 关键字段（如 Id, CreatedAt）使用 init 访问器。
3.2 DTO (Data Transfer Object) 规范
用途： API 端点与客户端之间的数据传输。
特性： 纯数据容器，无业务逻辑。轻量级，只包含客户端所需字段，剔除敏感/不必要数据。实体投影。
类型： 强制使用 C# 12 record 类型（利用不可变性、基于值相等性、主构造函数）。
3.3 EF Core 数据库映射 (强制 Fluent API)
方式： 仅使用 Fluent API。严禁实体类上使用 Data Annotations ([Table], [Key] 等)。
结构： 每个实体创建 IEntityTypeConfiguration<TEntity> 类，置于对应实体cs文件中。DbContext 中使用 ApplyConfigurationsFromAssembly() 加载。
核心配置：
	ToTable("TableName", "SchemaName")
	HasKey() (自增 ValueGeneratedOnAdd())
	Property().HasColumnName().HasColumnType()
	非空属性 IsRequired()
	字符串 HasMaxLength()
	小数 HasPrecision()
	关系 HasOne/WithOne/WithMany/HasForeignKey()/OnDelete() (严禁 [ForeignKey] 特性)
	默认值 HasDefaultValueSql() / HasDefaultValue()
	索引 HasIndex() (必要时 IsUnique())
	乐观并发 IsRowVersion()
	全局查询过滤 HasQueryFilter() (如软删除)
一致性： C# 类型 (含可空性)、默认值、集合类型与数据库列约束严格一致。
数据访问： 直接注入 DbContext 到端点/服务。禁用仓储模式。
性能： 只读操作 AsNoTracking()。警惕 N+1，用 Include/Select。复杂频繁查询考虑编译查询。
3.4 实体与 DTO 转换 (强制手动/显式)
机制： 必须手动编写双向转换逻辑。推荐 LINQ Select 投影 (实体到 DTO)。严禁 AutoMapper。
细节：映射到 DTO 时，只选择客户端所需字段，忽略敏感/不必要字段。处理可空性差异 (如 string? 到 string 提供默认值 string.Empty 或空检查)。映射逻辑中严禁引入业务计算或验证。

4. 类型与初始化规范
4.1 可空引用类型 (NRT)
启用： 项目文件 (.csproj) 强制启用 <Nullable>enable</Nullable>。
选择：
	数据库 NOT NULL：实体属性非可空类型。
	数据库 NULL：实体属性可空类型。
	单数导航属性：强制关系非可空，可选关系可空。
	集合导航属性：非可空集合类型，初始化为 [] 或 new List<T>()。严禁 null。
语义： 可空性准确反映业务含义。
4.2 默认值
必要性： 对非可空类型但无强制初始化的字段，必须提供默认值。
选择：
	字符串：必填通常 string.Empty。
	集合：必须初始化为空集合 ([])，严禁 null。
	日期/时间：通过数据库默认值 (HasDefaultValueSql()) 或 DateTime.UtcNow。
特殊： 复杂/动态默认值通过构造函数或 HasDefaultValueSql()。
4.3 required 关键字 (C# 12)
作用： 编译时强制属性在对象初始化时显式赋值。
与 FluentValidation 区别： required (编译时赋值检查) vs FluentValidation (运行时值有效性检查)。两者协同。
适用： 实体（非自增必填字段），DTO（record 类型主构造函数）。
限制： 不能与 = 默认值 结合。不影响 EF Core 映射，仍需 IsRequired()。
最佳实践： 仅对创建时必须显式赋值且无合理默认值的必填字段使用。同时配合 FluentValidation 和 Fluent API IsRequired()。

5. 验证规范 (强制 FluentValidation)
5.1 验证职责与实现
DTO 验证 (输入)： 验证客户端输入数据格式、基本结构。
	仅使用 FluentValidation。为每个 DTO 创建 AbstractValidator<TDto>。严禁 DTO 上使用 Data Annotations。
	验证器与 DTO 同文件。
	执行时机：API 层 (Minimal API 端点处理程序)。
实体验证 (业务/持久化)： 验证领域业务规则和数据库持久化完整性。
	仅使用 FluentValidation。为实体创建 AbstractValidator<TEntity> (不依赖实体内部方法)。
	验证器与实体服务文件附近或 Validators 文件夹。
	执行时机：数据持久化前 (服务层或 Minimal API 端点处理程序)。
5.2 验证分工
DTO 验证：输入数据格式和基本完整性。
实体验证：业务逻辑和持久化完整性。
分层：API 层负责 DTO 验证，服务层/处理程序负责实体验证。

6. API 设计 (Minimal API)
6.1 通用设计
原则： 遵循 RESTful。
路由： 仅 MapGet, MapPost，用 MapGroup 组织。
横切关注点： 使用 Minimal API 过滤器 (IEndpointFilter) 或 ASP.NET Core 中间件。
6.2 统一响应格式与错误处理
核心原则： 所有服务层结果及 API 响应，必须采用预定义、结构化格式。
关键类型定义：
	ServiceResult<T> (服务层带数据输出)：bool IsSuccess, T? Data, string? ErrorMessage, string? ErrorCode (可选), List<ValidationError>? ValidationErrors (可选)。通过静态工厂方法创建。
	ServiceResult (服务层无数据输出)：bool IsSuccess, string? ErrorMessage, string? ErrorCode (可选), List<ValidationError>? ValidationErrors (可选)。通过静态工厂方法创建。
	ApiResponse<T> (API 层带数据响应主体)：bool IsSuccess, T? Data, ErrorDetails? Error。
	ApiResponse (API 层无数据响应主体)：bool IsSuccess, ErrorDetails? Error。
	ErrorDetails (API 错误详情)：string Message, string? Code (可选), List<ValidationError>? ValidationErrors (可选)。
	ValidationError (字段验证错误)：string Field, string Message。
服务层开发规则：
	返回类型： 带数据返回 ServiceResult<T>，无数据返回 ServiceResult。
	禁止业务异常： 严禁通过抛异常表示预期业务失败。必须返回 IsSuccess=false 的 ServiceResult。
	业务验证： 验证失败时，构建 IsSuccess=false 的 ServiceResult，填充 ErrorMessage 和 ValidationErrors。
API 层开发规则 (端点处理)：
	输入验证： 所有接收输入的 API 端点，必须在调用服务层前执行 DTO 验证。
	DTO 验证失败： 立即构建 IsSuccess=false 的非泛型 ApiResponse，填充 ErrorDetails.ValidationErrors，返回 400 Bad Request。不得调用服务层。
	服务层结果映射：
		成功 (带数据)： ServiceResult<T> 且 IsSuccess=true -> ApiResponse<T> (IsSuccess=true, Data 赋值)。返回 2xx (200 OK, 201 Created)。
		成功 (无数据)： ServiceResult 且 IsSuccess=true -> 非泛型 ApiResponse (IsSuccess=true)。返回 2xx (200 OK, 204 No Content)。
		失败： ServiceResult (IsSuccess=false) -> 非泛型 ApiResponse (IsSuccess=false, Error 填充 ErrorMessage, ErrorCode, ValidationErrors)。
	HTTP 状态码 (失败)： 根据失败性质选择最语义化的 4xx 状态码：
		业务验证失败 (ValidationErrors 非空) / 无效输入：400 Bad Request 或 422 Unprocessable Entity。
		资源未找到：404 Not Found。
		业务冲突：409 Conflict。
		其他客户端预期业务失败：通常 400 Bad Request。
成功状态指示： ApiResponse.IsSuccess = true 且 HTTP 状态码为 2xx。不强制顶层 ApiResponse 包含通用“操作成功”文本。
错误处理 (系统错误)：
	全局异常捕获： 应用必须配置全局异常处理机制，捕获所有未显式处理的异常 (系统错误)。
	系统错误响应： 全局异常处理器将系统异常转换为标准的非泛型 ApiResponse。IsSuccess = false，通用错误消息（不暴露内部技术细节），返回 500 Internal Server Error。严禁包含异常堆栈/敏感详情。ValidationErrors 通常为 null 或空。
结果转换： 服务层 ServiceResult 必须可靠地转换为 API 层 ApiResponse。

7. 性能优化
异步编程： 所有 I/O 操作强制 async/await。
EF Core 查询： 只读操作 AsNoTracking()。警惕 N+1 问题，使用 Include/ThenInclude 或 Select 投影。
数据量控制： 大数据量 API 实现分页 (Skip()/Take()) 和排序。
缓存： 按需使用 IMemoryCache。

8. 关键架构约定
DI： 广泛使用 ASP.NET Core 内置 DI 容器。
配置： 使用强类型配置 (IOptions<T>等)。
后台任务： 使用 IHostedService / BackgroundService。
NuGet 包： 谨慎选择，定期审查更新。

9. 安全性
认证/授权： 强制配置/使用 ASP.NET Core 认证/授权。
传输： 生产环境强制 HTTPS。
CORS： 配置最小必要权限。
输入： 严格验证所有外部输入。必要时清理。
速率限制： 按需为 API 端点实现。

10. 强制执行
所有新开发代码必须严格遵守。现有代码修改时逐步重构。代码审查强制检查遵守情况。