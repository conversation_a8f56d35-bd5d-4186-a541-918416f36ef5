using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class SystemLimitService
{
    public async Task<ServiceResult<LimitConfigResponse>> GetLimitConfigAsync()
    {
        await Task.Delay(1);
        return ServiceResult<LimitConfigResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> UpdateLimitConfigAsync(UpdateLimitConfigRequest request)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<UserLimitsResponse>> GetUserLimitsAsync()
    {
        await Task.Delay(1);
        return ServiceResult<UserLimitsResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> UpdateUserLimitsAsync(UpdateUserLimitsRequest request)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }
}