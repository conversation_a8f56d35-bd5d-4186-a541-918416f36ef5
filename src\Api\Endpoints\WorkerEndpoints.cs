using Api.Extensions;
using Api.Services;
using Shared.DTOs;

namespace Api.Endpoints;

public static class WorkerEndpoints
{
    public static void MapWorkerNodeEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/worker-nodes").WithTags("WorkerNode Management").WithValidation();

        // ==================== 节点查询端点 ====================

        group.MapGet("/", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodeListAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点列表").WithDescription("获取所有工作节点的详细信息，包括状态、硬件信息、性能指标等");

        group.MapGet("/{nodeId}", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerByIdAsync(nodeId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取指定工作节点信息").WithDescription("获取指定ID工作节点的详细信息");

        group.MapGet("/online", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetOnlineWorkersAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取在线工作节点列表").WithDescription("获取所有在线状态的工作节点列表");

        // ==================== 统计和性能端点 ====================

        group.MapGet("/stats", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodeStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点统计").WithDescription("获取工作节点的基本统计信息，包括总数、在线数、健康状态分布等");

        group.MapGet("/performance", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.GetWorkerNodePerformanceAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点性能统计").WithDescription("获取工作节点的详细性能统计，包括CPU、内存、磁盘使用率等");

        // ==================== 节点管理端点 ====================

        group.MapPost("/", async Task<IResult> (AddWorkerNodeRequest request, WorkerService workerService) =>
        {
            var result = await workerService.AddWorkerNodeAsync(request);
            if (result.IsSuccess)
                return Results.Created($"/api/worker-nodes/{result.Data!.Id}", ResultExtensions.ApiOk(result.Data!, "工作节点添加成功"));
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("添加工作节点").WithDescription("手动添加新的工作节点到集群，需要提供节点名称和基础URL");

        group.MapPost("/{nodeId}/enable", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.EnableWorkerNodeAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点已启用");
        }).WithSummary("启用工作节点").WithDescription("手动启用指定的工作节点，使其可以接收新任务");

        group.MapPost("/{nodeId}/disable", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.DisableWorkerNodeAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点已禁用");
        }).WithSummary("禁用工作节点").WithDescription("手动禁用指定的工作节点，停止分配新任务");

        group.MapPost("/{nodeId}/toggle", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.ToggleWorkerNodeAsync(nodeId);
            if (result.IsSuccess)
            {
                var message = result.Data! ? "工作节点已启用" : "工作节点已禁用";
                return ResultExtensions.ApiOk(new { enabled = result.Data }, message);
            }

            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("切换工作节点状态").WithDescription("切换指定工作节点的启用/禁用状态");

        group.MapPost("/{nodeId}/maintenance", async Task<IResult> (Guid nodeId, MaintenanceModeRequest request, WorkerService workerService) =>
        {
            var result = await workerService.SetWorkerMaintenanceModeAsync(nodeId, request.MaintenanceMode);
            var action = request.MaintenanceMode ? "进入" : "退出";
            return ResultExtensions.ToHttpResultOrOk(result, $"工作节点已{action}维护模式");
        }).WithSummary("设置维护模式").WithDescription("设置工作节点进入或退出维护模式");

        // ==================== 健康检查和监控端点 ====================

        group.MapPost("/health-check", async Task<IResult> (WorkerService workerService) =>
        {
            var result = await workerService.HealthCheckAllWorkersAsync();
            if (result.IsSuccess)
                return ResultExtensions.ApiOk(new { healthyCount = result.Data }, $"健康检查完成，{result.Data} 个节点健康");
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("健康检查所有工作节点").WithDescription("对所有活跃的工作节点执行健康检查，返回健康节点数量");

        group.MapGet("/{nodeId}/metrics", async Task<IResult> (Guid nodeId, MonitoringService monitoringService) =>
        {
            var result = await monitoringService.GetWorkerMetricsAsync(nodeId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点指标").WithDescription("获取指定工作节点的详细性能指标历史数据");

        group.MapGet("/{nodeId}/alerts", async Task<IResult> (Guid nodeId, MonitoringService monitoringService, int pageSize = 20, int pageNumber = 1) =>
        {
            var result = await monitoringService.GetWorkerAlertsAsync(nodeId, pageSize, pageNumber);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点告警").WithDescription("获取指定工作节点的告警记录，支持分页查询");

        // ==================== 工作节点注册和心跳端点 ====================

        group.MapPost("/register", async Task<IResult> (WorkerNodeRegistration registration, WorkerService workerService) =>
        {
            var request = new AddWorkerNodeRequest(registration.Name, registration.BaseUrl);
            var result = await workerService.AddWorkerNodeAsync(request);

            if (result.IsSuccess)
            {
                var response = new RegistrationResponse(true, result.Data!.Id.ToString(), null);
                return ResultExtensions.ApiOk(response, "工作节点注册成功");
            }
            else
            {
                var response = new RegistrationResponse(false, null, result.ErrorMessage);
                return Results.BadRequest(response);
            }
        }).WithSummary("工作节点自动注册").WithDescription("工作节点启动时自动向核心服务注册，返回节点ID");

        group.MapPost("/{nodeId}/heartbeat", async Task<IResult> (Guid nodeId, NodeHeartbeat heartbeat, WorkerService workerService) =>
        {
            var result = await workerService.UpdateNodeHeartbeatAsync(nodeId, heartbeat);
            return ResultExtensions.ToHttpResultOrOk(result, "心跳接收成功");
        }).WithSummary("接收工作节点心跳").WithDescription("接收工作节点发送的心跳和性能指标，更新节点状态");

        group.MapPost("/{nodeId}/unregister", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.MarkNodeOfflineAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点注销成功");
        }).WithSummary("工作节点注销").WithDescription("工作节点停止时注销，标记为离线状态");

        // ==================== 高级管理端点 ====================

        group.MapGet("/capacity", async Task<IResult> (WorkerNodeManagementService managementService) =>
        {
            var result = await managementService.GetWorkerCapacityAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点容量信息").WithDescription("获取所有在线工作节点的容量和负载信息");

        group.MapPost("/{nodeId}/drain", async Task<IResult> (Guid nodeId, WorkerNodeManagementService managementService) =>
        {
            var result = await managementService.DrainWorkerNodeAsync(nodeId);
            return ResultExtensions.ToHttpResultOrOk(result, "工作节点开始排空");
        }).WithSummary("排空工作节点").WithDescription("将工作节点设置为排空状态，不再接收新任务并迁移现有任务");

        group.MapPost("/rebalance", async Task<IResult> (WorkerNodeManagementService managementService) =>
        {
            var result = await managementService.RebalanceWorkloadAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "工作负载重新平衡完成");
        }).WithSummary("重新平衡工作负载").WithDescription("在所有工作节点之间重新平衡任务分配");

        // ==================== 删除和重启端点 ====================

        group.MapDelete("/{nodeId}", async Task<IResult> (Guid nodeId, bool force, WorkerService workerService) =>
        {
            var result = await workerService.DeleteWorkerNodeAsync(nodeId, force);
            if (result.IsSuccess) return ResultExtensions.ApiOk(result.Data!, "工作节点删除成功");
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("删除工作节点").WithDescription("删除指定的工作节点及其所有相关数据，使用force=true强制删除有活跃任务的节点");

        group.MapPost("/{nodeId}/restart", async Task<IResult> (Guid nodeId, WorkerService workerService) =>
        {
            var result = await workerService.RestartWorkerAsync(nodeId);
            if (result.IsSuccess) return ResultExtensions.ApiOk(result.Data!, "重启命令发送成功");
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("重启工作节点").WithDescription("发送重启命令到指定的工作节点，节点将优雅关闭并重新启动");
    }
}