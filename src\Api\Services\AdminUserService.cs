using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AdminUserService
{
    public async Task<ServiceResult<PagedResponse<AdminUserListItemResponse>>> GetUsersAsync(int page, int pageSize, string? search, UserType? userType,
        UserAccountStatus? status)
    {
        await Task.Delay(1);
        return ServiceResult<PagedResponse<AdminUserListItemResponse>>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<AdminUserDetailResponse>> GetUserDetailAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult<AdminUserDetailResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> DisableUserAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> EnableUserAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    

    public async Task<ServiceResult> UpdateUserPlanAsync(Guid userId, UpdateUserPlanRequest request)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> DeleteUserAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<AdminUserStatsResponse>> GetUserStatsAsync()
    {
        await Task.Delay(1);
        return ServiceResult<AdminUserStatsResponse>.Failure("功能暂未实现");
    }
}