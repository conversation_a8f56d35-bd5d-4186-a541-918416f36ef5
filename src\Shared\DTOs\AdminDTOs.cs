using Shared.Common;

namespace Shared.DTOs;

// ==================== 管理员认证 ====================

public record AdminLoginRequest(string Username, string Password);

public record AdminLoginResponse(string Token, string RefreshToken, DateTime ExpiresAt);

public record AdminAuthStatusResponse(bool IsAuthenticated, string? Username, List<string> Permissions);

// ==================== 管理员仪表盘 ====================

public record AdminDashboardResponse(
    AdminKeyStatsResponse Stats,
    List<AdminRecentActivityResponse> RecentActivities,
    List<AdminSystemAlertResponse> SystemAlerts);

public record AdminKeyStatsResponse(int TotalUsers, int ActiveUsers, int TotalTasks, int ActiveTasks, int TotalWorkers, int HealthyWorkers);

public record AdminRecentActivityResponse(string Action, string Description, DateTime Timestamp, string? UserId);

public record AdminSystemAlertResponse(string Type, string Message, string Severity, DateTime Timestamp);

// ==================== 用户管理 ====================

public record AdminUserListItemResponse(
    Guid Id,
    string? Email,
    UserType UserType,
    UserPlanType PlanType,
    UserAccountStatus Status,
    DateTime CreatedAt,
    DateTime LastActiveAt,
    int TaskCount);

public record AdminUserDetailResponse(
    Guid Id,
    string? Email,
    UserType UserType,
    UserPlanType PlanType,
    UserAccountStatus Status,
    DateTime CreatedAt,
    DateTime LastActiveAt,
    DateTime UpdatedAt,
    AdminUserStatsDetailResponse Stats);

public record AdminUserStatsDetailResponse(int TotalTasks, int CompletedTasks, int FailedTasks, long TotalDownloadSize, DateTime? LastTaskAt);

public record UpdateUserPlanRequest(UserPlanType PlanType, DateTime? ExpiresAt);

public record AdminUserStatsResponse(int TotalUsers, int AnonymousUsers, int RegisteredUsers, int FreeUsers, int PremiumUsers, int ActiveToday, int NewToday);

// ==================== 任务管理 ====================

public record AdminTaskListItemResponse(
    Guid Id,
    string Title,
    WorkerTaskType Type,
    WorkerTaskStatus Status,
    Guid UserId,
    string? UserEmail,
    DateTime CreatedAt,
    DateTime? CompletedAt,
    int Progress);

public record AdminTaskDetailResponse(
    Guid Id,
    string Title,
    WorkerTaskType Type,
    WorkerTaskStatus Status,
    Guid UserId,
    string? UserEmail,
    DateTime CreatedAt,
    DateTime? StartedAt,
    DateTime? CompletedAt,
    int Progress,
    string? ErrorMessage,
    string? WorkerNodeId,
    Dictionary<string, object> Metadata);

public record TaskQueueStatusResponse(int PendingTasks, int ProcessingTasks, int QueuedTasks, List<QueueInfoResponse> Queues);

public record QueueInfoResponse(string Name, int Count, string Status);

// ==================== 日志管理 ====================

public record SystemLogResponse(DateTime Timestamp, string Level, string Message, string? Exception, Dictionary<string, object> Properties);

public record ErrorLogResponse(DateTime Timestamp, string Message, string? Exception, string? StackTrace, string? Source);

public record AuditLogResponse(DateTime Timestamp, string Action, Guid? UserId, string? UserEmail, string? IpAddress, Dictionary<string, object> Details);

// ==================== 通用响应 ====================

public record PagedResponse<T>(List<T> Items, int Page, int PageSize, int TotalCount, int TotalPages);