﻿using System.Text.Json;
using Api.Data;
using Api.Data.Models;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;
using Shared.Messages;

namespace Api.Services;

public class TaskService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<TaskService> _logger;
    private readonly IPublishEndpoint _publishEndpoint;
    private readonly YouTubeService _youTubeService;

    public TaskService(AppDbContext dbContext, ILogger<TaskService> logger, YouTubeService youTubeService, IPublishEndpoint publishEndpoint)
    {
        _dbContext = dbContext;
        _logger = logger;
        _youTubeService = youTubeService;
        _publishEndpoint = publishEndpoint;
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateVideoTaskAsync(CreateVideoTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"下载视频: {videoInfo.Title}",
                TaskType = WorkerTaskType.VideoDownload,
                Status = WorkerTaskStatus.Pending,
                Priority = request.Priority ?? WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                OutputFormat = request.OutputFormat,
                Quality = request.Quality,
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId,
                    request.OutputFormat,
                    request.Quality,
                    request.StartTime,
                    request.EndTime
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync(); // 保存状态更新

            _logger.LogInformation("已为用户 {UserId} 创建视频任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建视频任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<BatchTaskResponse>> CreateBatchTaskAsync(CreateBatchTaskRequest request, Guid userId)
    {
        try
        {
            List<string> videoIds;
            string batchTitle;

            if (request.Type == BatchTaskType.Playlist)
            {
                var playlistResult = await _youTubeService.GetPlaylistInfoAsync(request.SourceId!, request.MaxVideos);
                if (!playlistResult.IsSuccess)
                    return ServiceResult<BatchTaskResponse>.Failure($"无法获取播放列表信息: {playlistResult.ErrorMessage}", playlistResult.ErrorCode);

                videoIds = playlistResult.Data!.Videos.Select(v => v.VideoId).ToList();
                batchTitle = $"播放列表: {playlistResult.Data.Title}";
            }
            else if (request.Type == BatchTaskType.Channel)
            {
                var channelResult = await _youTubeService.GetChannelInfoAsync(request.SourceId!, request.MaxVideos);
                if (!channelResult.IsSuccess)
                    return ServiceResult<BatchTaskResponse>.Failure($"无法获取频道信息: {channelResult.ErrorMessage}", channelResult.ErrorCode);

                videoIds = channelResult.Data!.Videos.Select(v => v.VideoId).ToList();
                batchTitle = $"频道: {channelResult.Data.Title}";
            }
            else // VideoList
            {
                videoIds = request.VideoIds ?? new List<string>();
                batchTitle = $"自定义视频列表 ({videoIds.Count} 个视频)";
            }

            if (!videoIds.Any()) return ServiceResult<BatchTaskResponse>.Failure("没有找到可下载的视频");

            // 创建批量任务
            var batchTask = new BatchTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = batchTitle,
                SourceType = (BatchTaskType)(int)request.Type,
                SourceId = request.SourceId ?? string.Empty,
                SourceUrl = request.SourceUrl ?? string.Empty,
                TotalVideoCount = videoIds.Count,
                SelectedVideoCount = videoIds.Count,
                CompletedTaskCount = 0,
                FailedTaskCount = 0,
                Status = BatchTaskStatus.Created,
                Configuration = JsonSerializer.Serialize(new
                {
                    request.OutputFormat,
                    request.Quality,
                    request.StartTime,
                    request.EndTime,
                    request.MaxVideos
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.BatchTasks.Add(batchTask);

            // 创建子任务
            var workerTasks = new List<WorkerTask>();
            foreach (var videoId in videoIds)
            {
                var workerTask = new WorkerTask
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    BatchTaskId = batchTask.Id,
                    Name = $"批量下载视频: {videoId}",
                    TaskType = WorkerTaskType.VideoDownload,
                    Status = WorkerTaskStatus.Pending,
                    Priority = request.Priority ?? WorkerTaskPriority.Normal,
                    VideoId = videoId,
                    VideoUrl = $"https://www.youtube.com/watch?v={videoId}",
                    OutputFormat = request.OutputFormat,
                    Quality = request.Quality,
                    StartTime = request.StartTime,
                    EndTime = request.EndTime,
                    Parameters = JsonSerializer.Serialize(new
                    {
                        VideoId = videoId,
                        request.OutputFormat,
                        request.Quality,
                        request.StartTime,
                        request.EndTime
                    }),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                workerTasks.Add(workerTask);
            }

            _dbContext.WorkerTasks.AddRange(workerTasks);
            await _dbContext.SaveChangesAsync();

            // 立即发布所有子任务到消息队列
            foreach (var workerTask in workerTasks) await PublishTaskToQueueAsync(workerTask);
            await _dbContext.SaveChangesAsync(); // 保存状态更新

            _logger.LogInformation("已为用户 {UserId} 创建批量任务 {BatchTaskId}，包含 {Count} 个视频", userId, batchTask.Id, videoIds.Count);

            var response = MapToBatchTaskResponse(batchTask, workerTasks);
            return ServiceResult<BatchTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建批量任务时发生错误", userId);
            return ServiceResult<BatchTaskResponse>.Failure("创建批量任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<TaskListResponse>> GetUserTasksAsync(Guid userId, int page = 1, int pageSize = 20)
    {
        try
        {
            var skip = (page - 1) * pageSize;

            // 获取单个任务
            var workerTasks = await _dbContext.WorkerTasks.Where(wt => wt.UserId == userId && wt.BatchTaskId == null).OrderByDescending(wt => wt.CreatedAt)
                .Skip(skip).Take(pageSize).ToListAsync();

            // 获取批量任务
            var batchTasks = await _dbContext.BatchTasks.Where(bt => bt.UserId == userId).OrderByDescending(bt => bt.CreatedAt).Skip(skip).Take(pageSize)
                .ToListAsync();

            // 获取总数
            var totalWorkerTasks = await _dbContext.WorkerTasks.Where(wt => wt.UserId == userId && wt.BatchTaskId == null).CountAsync();

            var totalBatchTasks = await _dbContext.BatchTasks.Where(bt => bt.UserId == userId).CountAsync();

            var workerTaskResponses = workerTasks.Select(MapToWorkerTaskResponse).ToList();
            var batchTaskResponses = batchTasks.Select(bt => MapToBatchTaskResponse(bt, new List<WorkerTask>())).ToList();

            var response = new TaskListResponse(workerTaskResponses, batchTaskResponses, totalWorkerTasks + totalBatchTasks, page, pageSize);

            return ServiceResult<TaskListResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户 {UserId} 任务列表时发生错误", userId);
            return ServiceResult<TaskListResponse>.Failure("获取任务列表失败");
        }
    }

    public async Task<ServiceResult<WorkerTaskResponse>> GetTaskAsync(Guid taskId, Guid userId)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId).FirstOrDefaultAsync();

            if (task == null) return ServiceResult<WorkerTaskResponse>.Failure("任务不存在", "TASK_NOT_FOUND");

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取任务 {TaskId} 详情时发生错误", taskId);
            return ServiceResult<WorkerTaskResponse>.Failure("获取任务详情失败");
        }
    }

    public async Task<ServiceResult<BatchTaskResponse>> GetBatchTaskAsync(Guid batchTaskId, Guid userId)
    {
        try
        {
            var batchTask = await _dbContext.BatchTasks.Where(bt => bt.Id == batchTaskId && bt.UserId == userId).FirstOrDefaultAsync();

            if (batchTask == null) return ServiceResult<BatchTaskResponse>.Failure("批量任务不存在", "BATCH_TASK_NOT_FOUND");

            // 获取子任务
            var workerTasks = await _dbContext.WorkerTasks.Where(wt => wt.BatchTaskId == batchTaskId).OrderBy(wt => wt.CreatedAt).ToListAsync();

            var response = MapToBatchTaskResponse(batchTask, workerTasks);
            return ServiceResult<BatchTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取批量任务 {BatchTaskId} 详情时发生错误", batchTaskId);
            return ServiceResult<BatchTaskResponse>.Failure("获取批量任务详情失败");
        }
    }

    public async Task<ServiceResult> CancelTaskAsync(Guid taskId, Guid userId)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId).FirstOrDefaultAsync();

            if (task == null) return ServiceResult.Failure("任务不存在", "TASK_NOT_FOUND");

            // 只有待处理和排队中的任务可以取消
            if (task.Status != WorkerTaskStatus.Pending && task.Status != WorkerTaskStatus.Queued) return ServiceResult.Failure("任务已开始处理，无法取消");

            task.Status = WorkerTaskStatus.Cancelled;
            task.UpdatedAt = DateTime.UtcNow;
            task.CompletedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 取消任务 {TaskId}", userId, taskId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消任务 {TaskId} 时发生错误", taskId);
            return ServiceResult.Failure("取消任务失败");
        }
    }

    public async Task<ServiceResult> CancelBatchTaskAsync(Guid batchTaskId, Guid userId)
    {
        try
        {
            var batchTask = await _dbContext.BatchTasks.Where(bt => bt.Id == batchTaskId && bt.UserId == userId).FirstOrDefaultAsync();

            if (batchTask == null) return ServiceResult.Failure("批量任务不存在", "BATCH_TASK_NOT_FOUND");

            // 只有待处理和进行中的批量任务可以取消
            if (batchTask.Status == BatchTaskStatus.Completed || batchTask.Status == BatchTaskStatus.Cancelled) return ServiceResult.Failure("批量任务已完成或已取消");

            // 取消所有未完成的子任务
            await _dbContext.WorkerTasks
                .Where(wt => wt.BatchTaskId == batchTaskId && (wt.Status == WorkerTaskStatus.Pending || wt.Status == WorkerTaskStatus.Queued))
                .ExecuteUpdateAsync(wt => wt
                    .SetProperty(x => x.Status, WorkerTaskStatus.Cancelled).SetProperty(x => x.UpdatedAt, DateTime.UtcNow)
                    .SetProperty(x => x.CompletedAt, DateTime.UtcNow));

            // 更新批量任务状态
            batchTask.Status = BatchTaskStatus.Cancelled;
            batchTask.UpdatedAt = DateTime.UtcNow;
            batchTask.CompletedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 取消批量任务 {BatchTaskId}", userId, batchTaskId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消批量任务 {BatchTaskId} 时发生错误", batchTaskId);
            return ServiceResult.Failure("取消批量任务失败");
        }
    }

    public async Task<ServiceResult<TaskStatsResponse>> GetUserTaskStatsAsync(Guid userId)
    {
        try
        {
            // 获取单个任务统计
            var workerTaskStats = await _dbContext.WorkerTasks.Where(wt => wt.UserId == userId).GroupBy(wt => 1).Select(g => new
            {
                Total = g.Count(),
                Pending = g.Count(wt => wt.Status == WorkerTaskStatus.Pending),
                Queued = g.Count(wt => wt.Status == WorkerTaskStatus.Queued),
                Processing = g.Count(wt => wt.Status == WorkerTaskStatus.Processing),
                Completed = g.Count(wt => wt.Status == WorkerTaskStatus.Completed),
                Failed = g.Count(wt => wt.Status == WorkerTaskStatus.Failed),
                Cancelled = g.Count(wt => wt.Status == WorkerTaskStatus.Cancelled)
            }).FirstOrDefaultAsync();

            // 获取批量任务统计
            var batchTaskStats = await _dbContext.BatchTasks.Where(bt => bt.UserId == userId).GroupBy(bt => 1).Select(g => new
            {
                Total = g.Count(),
                Pending = g.Count(bt => bt.Status == BatchTaskStatus.Created),
                Processing = g.Count(bt => bt.Status == BatchTaskStatus.Running),
                Completed = g.Count(bt => bt.Status == BatchTaskStatus.Completed),
                Failed = g.Count(bt => bt.Status == BatchTaskStatus.Failed),
                Cancelled = g.Count(bt => bt.Status == BatchTaskStatus.Cancelled)
            }).FirstOrDefaultAsync();

            var totalTasks = (workerTaskStats?.Total ?? 0) + (batchTaskStats?.Total ?? 0);
            var pendingTasks = (workerTaskStats?.Pending ?? 0) + (batchTaskStats?.Pending ?? 0);
            var processingTasks = (workerTaskStats?.Processing ?? 0) + (batchTaskStats?.Processing ?? 0);
            var completedTasks = (workerTaskStats?.Completed ?? 0) + (batchTaskStats?.Completed ?? 0);
            var failedTasks = (workerTaskStats?.Failed ?? 0) + (batchTaskStats?.Failed ?? 0);
            var cancelledTasks = (workerTaskStats?.Cancelled ?? 0) + (batchTaskStats?.Cancelled ?? 0);

            var stats = new TaskStatsResponse(totalTasks, pendingTasks, processingTasks, completedTasks, failedTasks, cancelledTasks);

            return ServiceResult<TaskStatsResponse>.Success(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户 {UserId} 任务统计时发生错误", userId);
            return ServiceResult<TaskStatsResponse>.Failure("获取任务统计失败");
        }
    }

    public async Task<ServiceResult<List<WorkerTaskResponse>>> GetPendingTasksAsync(int batchSize = 10)
    {
        try
        {
            var pendingTasks = await _dbContext.WorkerTasks.Where(wt => wt.Status == WorkerTaskStatus.Pending).OrderBy(wt => wt.Priority)
                .ThenBy(wt => wt.CreatedAt).Take(batchSize).ToListAsync();

            var taskResponses = pendingTasks.Select(MapToWorkerTaskResponse).ToList();
            return ServiceResult<List<WorkerTaskResponse>>.Success(taskResponses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取待处理任务时发生错误");
            return ServiceResult<List<WorkerTaskResponse>>.Failure("获取待处理任务失败");
        }
    }

    public async Task<ServiceResult> UpdateTaskStatusAsync(Guid taskId, WorkerTaskStatus status, string? errorMessage = null, int? progress = null)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId).FirstOrDefaultAsync();

            if (task == null) return ServiceResult.Failure("任务不存在", "TASK_NOT_FOUND");

            task.Status = status;
            task.UpdatedAt = DateTime.UtcNow;

            if (errorMessage != null) task.ErrorMessage = errorMessage;

            if (progress.HasValue) task.Progress = Math.Max(0, Math.Min(100, progress.Value));

            if (status == WorkerTaskStatus.Completed || status == WorkerTaskStatus.Failed || status == WorkerTaskStatus.Cancelled)
            {
                task.CompletedAt = DateTime.UtcNow;

                // 如果是批量任务的子任务，更新批量任务的统计
                if (task.BatchTaskId.HasValue) await UpdateBatchTaskProgressAsync(task.BatchTaskId.Value);
            }

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已更新任务 {TaskId} 状态为 {Status}", taskId, status);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新任务 {TaskId} 状态时发生错误", taskId);
            return ServiceResult.Failure("更新任务状态失败");
        }
    }

    private async Task UpdateBatchTaskProgressAsync(Guid batchTaskId)
    {
        try
        {
            var batchTask = await _dbContext.BatchTasks.Where(bt => bt.Id == batchTaskId).FirstOrDefaultAsync();

            if (batchTask == null) return;

            // 统计子任务状态
            var taskStats = await _dbContext.WorkerTasks.Where(wt => wt.BatchTaskId == batchTaskId).GroupBy(wt => 1).Select(g => new
            {
                Total = g.Count(),
                Completed = g.Count(wt => wt.Status == WorkerTaskStatus.Completed),
                Failed = g.Count(wt => wt.Status == WorkerTaskStatus.Failed),
                Processing = g.Count(wt => wt.Status == WorkerTaskStatus.Processing),
                Cancelled = g.Count(wt => wt.Status == WorkerTaskStatus.Cancelled)
            }).FirstOrDefaultAsync();

            if (taskStats == null) return;

            batchTask.CompletedTaskCount = taskStats.Completed;
            batchTask.FailedTaskCount = taskStats.Failed;
            batchTask.Progress = taskStats.Total > 0 ? taskStats.Completed * 100 / taskStats.Total : 0;
            batchTask.UpdatedAt = DateTime.UtcNow;

            // 更新批量任务状态
            if (taskStats.Completed + taskStats.Failed + taskStats.Cancelled == taskStats.Total)
            {
                // 所有子任务都已完成
                if (taskStats.Failed > 0)
                    batchTask.Status = BatchTaskStatus.Failed;
                else if (taskStats.Cancelled == taskStats.Total)
                    batchTask.Status = BatchTaskStatus.Cancelled;
                else
                    batchTask.Status = BatchTaskStatus.Completed;
                batchTask.CompletedAt = DateTime.UtcNow;
            }
            else if (taskStats.Processing > 0)
            {
                batchTask.Status = BatchTaskStatus.Running;
            }

            await _dbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新批量任务 {BatchTaskId} 进度时发生错误", batchTaskId);
        }
    }

    public async Task<ServiceResult> RetryTaskAsync(Guid taskId, Guid userId)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId).FirstOrDefaultAsync();

            if (task == null) return ServiceResult.Failure("任务不存在", "TASK_NOT_FOUND");

            // 只有失败的任务可以重试
            if (task.Status != WorkerTaskStatus.Failed) return ServiceResult.Failure("只有失败的任务可以重试");

            // 检查重试次数限制
            if (task.RetryCount >= 3) return ServiceResult.Failure("任务重试次数已达上限");

            // 重置任务状态
            task.Status = WorkerTaskStatus.Pending;
            task.Progress = 0;
            task.ErrorMessage = null;
            task.RetryCount++;
            task.UpdatedAt = DateTime.UtcNow;
            task.CompletedAt = null;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已重试任务 {TaskId}，重试次数: {RetryCount}", taskId, task.RetryCount);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重试任务 {TaskId} 时发生错误", taskId);
            return ServiceResult.Failure("重试任务失败");
        }
    }

    public async Task<ServiceResult> DeleteCompletedTaskAsync(Guid taskId, Guid userId)
    {
        try
        {
            var task = await _dbContext.WorkerTasks.Where(wt => wt.Id == taskId && wt.UserId == userId).FirstOrDefaultAsync();

            if (task == null) return ServiceResult.Failure("任务不存在", "TASK_NOT_FOUND");

            // 只有已完成、失败或取消的任务可以删除
            if (task.Status != WorkerTaskStatus.Completed && task.Status != WorkerTaskStatus.Failed && task.Status != WorkerTaskStatus.Cancelled)
                return ServiceResult.Failure("只有已完成的任务可以删除");

            _dbContext.WorkerTasks.Remove(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 删除任务 {TaskId}", userId, taskId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除任务 {TaskId} 时发生错误", taskId);
            return ServiceResult.Failure("删除任务失败");
        }
    }

    public async Task<ServiceResult<int>> CleanupCompletedTasksAsync(Guid userId, int daysOld = 7)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysOld);

            var deletedCount = await _dbContext.WorkerTasks.Where(wt =>
                wt.UserId == userId &&
                (wt.Status == WorkerTaskStatus.Completed || wt.Status == WorkerTaskStatus.Failed || wt.Status == WorkerTaskStatus.Cancelled) &&
                wt.CompletedAt < cutoffDate).ExecuteDeleteAsync();

            _logger.LogInformation("已为用户 {UserId} 清理 {Count} 个已完成任务", userId, deletedCount);
            return ServiceResult<int>.Success(deletedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 清理已完成任务时发生错误", userId);
            return ServiceResult<int>.Failure("清理已完成任务失败");
        }
    }

    private async Task PublishTaskToQueueAsync(WorkerTask task)
    {
        try
        {
            var taskMessage = new TaskMessage(task.Id, task.UserId, task.TaskType, task.Priority, task.VideoId, task.VideoUrl, task.OutputFormat, task.Quality,
                task.StartTime, task.EndTime, task.CreatedAt);

            await _publishEndpoint.Publish<ITaskMessage>(taskMessage);

            // 更新任务状态为排队中
            task.Status = WorkerTaskStatus.Queued;
            task.UpdatedAt = DateTime.UtcNow;

            _logger.LogInformation("已将任务 {TaskId} 发布到消息队列", task.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "将任务 {TaskId} 发布到消息队列时失败", task.Id);
            // 发布失败时保持Pending状态，等待后续重试
        }
    }


    private static WorkerTaskResponse MapToWorkerTaskResponse(WorkerTask task)
    {
        return new WorkerTaskResponse(task.Id, task.BatchTaskId, task.TaskType, task.Status, task.Priority, task.VideoId, task.VideoTitle, task.VideoUrl,
            task.OutputFormat, task.Quality, task.StartTime, task.EndTime, task.Progress, task.ErrorMessage, task.OutputFilePath, task.FileSize, task.CreatedAt,
            task.UpdatedAt, task.CompletedAt);
    }

    private static BatchTaskResponse MapToBatchTaskResponse(BatchTask batchTask, List<WorkerTask> workerTasks)
    {
        var taskResponses = workerTasks.Select(MapToWorkerTaskResponse).ToList();

        // 从配置JSON中解析输出格式和质量
        var config = JsonSerializer.Deserialize<Dictionary<string, object>>(batchTask.Configuration);
        var outputFormat = config?.GetValueOrDefault("OutputFormat")?.ToString();
        var quality = config?.GetValueOrDefault("Quality")?.ToString();

        return new BatchTaskResponse(batchTask.Id, (BatchTaskType)(int)batchTask.SourceType, batchTask.Name, batchTask.SourceId, batchTask.SourceUrl,
            batchTask.TotalVideoCount, batchTask.CompletedTaskCount, batchTask.FailedTaskCount, batchTask.Status, WorkerTaskPriority.Normal, // 从配置中解析或使用默认值
            outputFormat, quality, batchTask.Progress, batchTask.ErrorMessage, batchTask.CreatedAt, batchTask.UpdatedAt, batchTask.CompletedAt, taskResponses);
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateGifTaskAsync(CreateGifTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建GIF任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"制作GIF: {videoInfo.Title}",
                TaskType = WorkerTaskType.GifCreate,
                Status = WorkerTaskStatus.Pending,
                Priority = request.Priority ?? WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                OutputFormat = "gif",
                Quality = $"{request.Fps}:{request.Width}", // 格式: "fps:width"
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId,
                    request.StartTime,
                    request.EndTime,
                    request.Fps,
                    request.Width
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 创建GIF任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建GIF任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建GIF任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateRingtoneTaskAsync(CreateRingtoneTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建铃声任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"制作铃声: {videoInfo.Title}",
                TaskType = WorkerTaskType.RingtoneCreate,
                Status = WorkerTaskStatus.Pending,
                Priority = request.Priority ?? WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                OutputFormat = request.OutputFormat,
                Quality = "128k", // 固定128kbps
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId,
                    request.StartTime,
                    request.EndTime,
                    request.OutputFormat,
                    request.EnableFade
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 创建铃声任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建铃声任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建铃声任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateCommentTaskAsync(CreateCommentTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建评论下载任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"下载评论: {videoInfo.Title}",
                TaskType = WorkerTaskType.CommentDownload,
                Status = WorkerTaskStatus.Pending,
                Priority = request.Priority ?? WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                OutputFormat = request.OutputFormat,
                Quality = request.CommentLimit.ToString(), // 使用Quality字段存储评论数量
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId,
                    request.OutputFormat,
                    request.CommentLimit
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 创建评论下载任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建评论下载任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建评论下载任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateDescriptionTaskAsync(CreateDescriptionTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建描述下载任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"下载描述: {videoInfo.Title}",
                TaskType = WorkerTaskType.DescriptionDownload,
                Status = WorkerTaskStatus.Pending,
                Priority = request.Priority ?? WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                OutputFormat = "txt",
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 创建描述下载任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建描述下载任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建描述下载任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateSubtitleTaskAsync(CreateSubtitleTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建字幕下载任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"下载字幕: {videoInfo.Title}",
                TaskType = WorkerTaskType.SubtitleDownload,
                Status = WorkerTaskStatus.Pending,
                Priority = request.Priority ?? WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                OutputFormat = request.OutputFormat,
                Quality = request.Language, // 使用Quality字段存储语言代码
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId,
                    request.OutputFormat,
                    request.Language
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 创建字幕下载任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建字幕下载任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建字幕下载任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateAudioTaskAsync(CreateAudioTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建音频提取任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"提取音频: {videoInfo.Title}",
                TaskType = WorkerTaskType.AudioConvert,
                Status = WorkerTaskStatus.Pending,
                Priority = WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                OutputFormat = request.OutputFormat,
                Quality = request.Quality,
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId,
                    request.OutputFormat,
                    request.Quality,
                    request.StartTime,
                    request.EndTime
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 创建音频提取任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建音频提取任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建音频提取任务失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<WorkerTaskResponse>> CreateThumbnailTaskAsync(CreateThumbnailTaskRequest request, Guid userId)
    {
        try
        {
            var videoInfoResult = await _youTubeService.GetVideoInfoAsync(request.VideoId);
            if (!videoInfoResult.IsSuccess)
                return ServiceResult<WorkerTaskResponse>.Failure($"无法获取视频信息: {videoInfoResult.ErrorMessage}", videoInfoResult.ErrorCode);

            var videoInfo = videoInfoResult.Data!;

            // 创建缩略图下载任务
            var task = new WorkerTask
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                Name = $"下载缩略图: {videoInfo.Title}",
                TaskType = WorkerTaskType.ThumbnailDownload,
                Status = WorkerTaskStatus.Pending,
                Priority = WorkerTaskPriority.Normal,
                VideoId = request.VideoId,
                VideoTitle = videoInfo.Title,
                VideoUrl = $"https://www.youtube.com/watch?v={request.VideoId}",
                Quality = request.Quality,
                Parameters = JsonSerializer.Serialize(new
                {
                    request.VideoId,
                    request.Quality
                }),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _dbContext.WorkerTasks.Add(task);
            await _dbContext.SaveChangesAsync();

            // 立即发布任务到消息队列
            await PublishTaskToQueueAsync(task);
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已为用户 {UserId} 创建缩略图下载任务: {TaskId}", userId, task.Id);

            var response = MapToWorkerTaskResponse(task);
            return ServiceResult<WorkerTaskResponse>.Success(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "为用户 {UserId} 创建缩略图下载任务时发生错误", userId);
            return ServiceResult<WorkerTaskResponse>.Failure("创建缩略图下载任务失败，请稍后重试");
        }
    }

    // ==================== 新增批量任务操作方法 ====================

    public async Task<ServiceResult> PauseBatchTaskAsync(Guid batchTaskId, Guid userId)
    {
        // TODO: 实现暂停批量任务逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> ResumeBatchTaskAsync(Guid batchTaskId, Guid userId)
    {
        // TODO: 实现恢复批量任务逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> CancelAllUserTasksAsync(Guid userId)
    {
        // TODO: 实现取消用户所有任务逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<TaskStatsResponse>> GetSystemTaskStatsAsync()
    {
        // TODO: 实现系统任务统计逻辑
        await Task.Delay(1);
        var stats = new TaskStatsResponse(0, 0, 0, 0, 0, 0);
        return ServiceResult<TaskStatsResponse>.Success(stats);
    }

    public async Task<ServiceResult> SystemCleanupAsync(int daysOld)
    {
        // TODO: 实现系统清理逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }
}