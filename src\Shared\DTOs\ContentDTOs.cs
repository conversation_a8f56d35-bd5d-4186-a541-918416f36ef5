namespace Shared.DTOs;

// ==================== 黑名单管理 ====================

public record BlacklistItemResponse(Guid Id, string Type, string Value, string Reason, string Status, DateTime CreatedAt, DateTime? ExpiresAt);

public record AddToBlacklistRequest(string Type, string Value, string Reason, DateTime? ExpiresAt);

public record UpdateBlacklistRequest(string? Reason, string? Status, DateTime? ExpiresAt);

public record CheckBlacklistRequest(string Type, string Value);

public record CheckBlacklistResponse(bool IsBlacklisted, string? Reason, DateTime? ExpiresAt);

// ==================== DMCA管理 ====================

public record DmcaNoticeResponse(
    Guid Id,
    string ClaimantName,
    string ClaimantEmail,
    string ContentUrl,
    string Description,
    string Status,
    DateTime SubmittedAt,
    DateTime? ProcessedAt);

public record SubmitDmcaNoticeRequest(string ClaimantName, string ClaimantEmail, string ContentUrl, string Description, string ContactInfo);

public record ProcessDmcaNoticeRequest(string Action, string? Notes);

public record RejectDmcaNoticeRequest(string Reason);

// ==================== 内容举报 ====================

public record ContentReportResponse(
    Guid Id,
    string ContentType,
    string ContentId,
    string ReportType,
    string Description,
    string Status,
    Guid ReporterId,
    DateTime SubmittedAt,
    DateTime? ProcessedAt);

public record SubmitContentReportRequest(string ContentType, string ContentId, string ReportType, string Description);

public record HandleContentReportRequest(string Action, string? Notes);

public record CloseContentReportRequest(string Resolution, string? Notes);

// ==================== 内容审核 ====================

public record ModerationQueueItemResponse(Guid Id, string ContentType, string ContentId, string Priority, string Status, DateTime CreatedAt, int ReportCount);

public record RejectContentRequest(string Reason);

public record FlagContentRequest(string Flag, string Reason);

// ==================== 内容分析 ====================

public record ContentStatsResponse(int TotalContent, int FlaggedContent, int RemovedContent, int ReportsToday, Dictionary<string, int> ContentByType);

public record ContentTrendsResponse(List<ContentTrendDataResponse> TrendData);

public record ContentTrendDataResponse(DateTime Date, int TotalContent, int FlaggedContent, int RemovedContent);

public record ScanContentRequest(string ContentType, string ContentId);

public record ScanContentResponse(bool IsViolation, List<string> Violations, double ConfidenceScore);

// ==================== 版权保护 ====================

public record CopyrightPolicyResponse(string Name, string Description, bool IsActive, DateTime LastUpdated);

public record CheckCopyrightRequest(string ContentType, string ContentId);

public record CheckCopyrightResponse(bool HasCopyrightIssue, List<string> Issues, string? RecommendedAction);

public record SubmitCopyrightClaimRequest(string ContentType, string ContentId, string ClaimantName, string ClaimantEmail, string Description, string Evidence);

public record CopyrightClaimResponse(Guid Id, string Status, DateTime SubmittedAt);