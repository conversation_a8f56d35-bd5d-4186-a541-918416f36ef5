using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class MonitoringPerformanceService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<MonitoringPerformanceService> _logger;

    public MonitoringPerformanceService(AppDbContext dbContext, ILogger<MonitoringPerformanceService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<ServiceResult<PerformanceSummaryResponse>> GetPerformanceSummaryAsync()
    {
        try
        {
            var now = DateTime.UtcNow;
            var last24Hours = now.AddHours(-24);
            var last7Days = now.AddDays(-7);

            // 获取最近24小时的平均性能指标
            var recent24hMetrics = await _dbContext.WorkerMetrics.Where(m => m.RecordedAt >= last24Hours).GroupBy(m => 1).Select(g => new
            {
                AvgCpu = g.Average(m => m.CpuUsagePercent),
                AvgMemory = g.Average(m => m.MemoryUsagePercent),
                AvgDisk = g.Average(m => m.DiskUsagePercent),
                MaxCpu = g.Max(m => m.CpuUsagePercent),
                MaxMemory = g.Max(m => m.MemoryUsagePercent),
                MaxDisk = g.Max(m => m.DiskUsagePercent)
            }).FirstOrDefaultAsync();

            // 获取最近7天的平均性能指标
            var recent7dMetrics = await _dbContext.WorkerMetrics.Where(m => m.RecordedAt >= last7Days).GroupBy(m => 1).Select(g => new
            {
                AvgCpu = g.Average(m => m.CpuUsagePercent),
                AvgMemory = g.Average(m => m.MemoryUsagePercent),
                AvgDisk = g.Average(m => m.DiskUsagePercent)
            }).FirstOrDefaultAsync();

            var summary = new PerformanceSummaryResponse(recent24hMetrics?.AvgCpu ?? 0, recent24hMetrics?.AvgMemory ?? 0, recent24hMetrics?.AvgDisk ?? 0,
                recent24hMetrics?.MaxCpu ?? 0, recent24hMetrics?.MaxMemory ?? 0, recent24hMetrics?.MaxDisk ?? 0, recent7dMetrics?.AvgCpu ?? 0,
                recent7dMetrics?.AvgMemory ?? 0, recent7dMetrics?.AvgDisk ?? 0, DateTime.UtcNow);

            return ServiceResult<PerformanceSummaryResponse>.Success(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取性能摘要时发生错误");
            return ServiceResult<PerformanceSummaryResponse>.Failure("获取性能摘要失败", "PERFORMANCE_SUMMARY_ERROR");
        }
    }

    public async Task<ServiceResult<PerformanceTrendsResponse>> GetPerformanceTrendsAsync(int days = 7)
    {
        try
        {
            var startDate = DateTime.UtcNow.AddDays(-days);

            var trends = await _dbContext.WorkerMetrics.Where(m => m.RecordedAt >= startDate).GroupBy(m => new { m.RecordedAt.Date, m.RecordedAt.Hour })
                .Select(g => new PerformanceTrendPoint(g.Key.Date.AddHours(g.Key.Hour), g.Average(m => m.CpuUsagePercent), g.Average(m => m.MemoryUsagePercent),
                    g.Average(m => m.DiskUsagePercent), (int)g.Average(m => m.ActiveTasks))).OrderBy(t => t.Timestamp).ToListAsync();

            var trendsResponse = new PerformanceTrendsResponse(startDate, DateTime.UtcNow, trends);

            return ServiceResult<PerformanceTrendsResponse>.Success(trendsResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取性能趋势时发生错误");
            return ServiceResult<PerformanceTrendsResponse>.Failure("获取性能趋势失败", "PERFORMANCE_TRENDS_ERROR");
        }
    }
}