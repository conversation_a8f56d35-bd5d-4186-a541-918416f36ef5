using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class SystemFeatureService
{
    public async Task<ServiceResult<List<FeatureToggleResponse>>> GetAllFeaturesAsync()
    {
        await Task.Delay(1);
        return ServiceResult<List<FeatureToggleResponse>>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> EnableFeatureAsync(string feature)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> DisableFeatureAsync(string feature)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> ToggleFeatureAsync(string feature)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }
}