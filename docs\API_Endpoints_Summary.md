# API端点完整总结

## 📋 概述

本文档总结了YouTube下载器项目的完整API端点设计，包括现有端点和新增端点。所有端点都遵循项目规范，仅使用GET和POST方法。

## 🔄 现有端点（已实现）

### 1. 认证相关 (`/api/auth`)

#### 基础认证
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/check` - 检查认证状态

#### 密码管理
- `POST /api/auth/change-password` - 修改密码
- `POST /api/auth/forgot-password` - 忘记密码
- `POST /api/auth/reset-password` - 重置密码

#### 邮箱验证
- `POST /api/auth/verify-email` - 验证邮箱
- `POST /api/auth/resend-verification` - 重新发送验证邮件

#### 用户信息管理
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/update-profile` - 更新用户资料

#### 会话管理
- `GET /api/auth/sessions` - 获取用户会话列表
- `POST /api/auth/sessions/{sessionId}/revoke` - 撤销指定会话
- `POST /api/auth/sessions/revoke-others` - 撤销所有其他会话

#### 账户管理
- `POST /api/auth/delete-account` - 删除账户

### 2. YouTube内容 (`/api/youtube`)

#### 内容获取
- `GET /api/youtube/video/{videoId}` - 获取YouTube视频信息
- `GET /api/youtube/playlist/{playlistId}` - 获取播放列表信息
- `GET /api/youtube/channel/{channelId}` - 获取频道信息

#### URL解析
- `POST /api/youtube/parse-url` - 解析YouTube URL
- `POST /api/youtube/fetch` - 智能获取YouTube内容

#### 搜索和发现
- `GET /api/youtube/search` - 搜索YouTube内容
- `GET /api/youtube/trending` - 获取热门视频
- `GET /api/youtube/categories` - 获取视频分类
- `GET /api/youtube/regions` - 获取支持的地区列表

#### 元数据管理
- `POST /api/youtube/refresh/{videoId}` - 刷新视频信息
- `GET /api/youtube/formats/{videoId}` - 获取视频格式信息

### 3. 任务管理 (`/api/tasks`)

#### 任务创建
- `POST /api/tasks/video` - 创建视频下载任务
- `POST /api/tasks/audio` - 创建音频转换任务
- `POST /api/tasks/gif` - 创建GIF制作任务
- `POST /api/tasks/subtitle` - 创建字幕下载任务
- `POST /api/tasks/thumbnail` - 创建缩略图下载任务
- `POST /api/tasks/comment` - 创建评论下载任务
- `POST /api/tasks/description` - 创建描述下载任务
- `POST /api/tasks/ringtone` - 创建铃声制作任务
- `POST /api/tasks/batch` - 创建批量任务

#### 任务查询
- `GET /api/tasks` - 获取用户任务列表
- `GET /api/tasks/{taskId}` - 获取任务详情
- `GET /api/tasks/{taskId}/progress` - 获取任务进度
- `GET /api/tasks/batch/{batchTaskId}` - 获取批量任务详情
- `GET /api/tasks/batch/{batchTaskId}/subtasks` - 获取批量任务子任务列表

#### 任务操作
- `POST /api/tasks/{taskId}/cancel` - 取消任务
- `POST /api/tasks/{taskId}/retry` - 重试任务
- `POST /api/tasks/{taskId}/pause` - 暂停任务
- `POST /api/tasks/{taskId}/resume` - 恢复任务
- `POST /api/tasks/batch/{batchTaskId}/pause` - 暂停批量任务
- `POST /api/tasks/batch/{batchTaskId}/resume` - 恢复批量任务

#### 批量操作
- `POST /api/tasks/cleanup` - 清理已完成任务
- `POST /api/tasks/cancel-all` - 取消所有任务

#### 管理员任务端点
- `GET /api/admin/tasks/pending` - 获取待处理任务
- `POST /api/admin/tasks/{taskId}/status` - 更新任务状态
- `GET /api/admin/tasks/stats/system` - 获取系统任务统计
- `POST /api/admin/tasks/maintenance/cleanup` - 系统维护清理

### 4. 文件下载 (`/api/files`)

#### 下载链接生成
- `GET /api/files/download/{taskId}` - 生成文件下载链接
- `POST /api/files/download/batch` - 批量生成下载链接

#### 文件信息管理
- `GET /api/files/info/{taskId}` - 获取文件信息
- `GET /api/files/list` - 获取用户文件列表

#### 文件清理
- `POST /api/files/cleanup` - 清理文件

### 5. 工作节点 (`/api/worker-nodes`)

#### 节点查询
- `GET /api/worker-nodes` - 获取工作节点列表
- `GET /api/worker-nodes/{nodeId}` - 获取节点详情
- `GET /api/worker-nodes/{nodeId}/status` - 获取节点状态

#### 节点管理
- `POST /api/worker-nodes/{nodeId}/start` - 启动节点
- `POST /api/worker-nodes/{nodeId}/stop` - 停止节点
- `POST /api/worker-nodes/{nodeId}/restart` - 重启节点
- `POST /api/worker-nodes/{nodeId}/drain` - 排空节点
- `POST /api/worker-nodes/{nodeId}/undrain` - 取消排空节点

#### 健康检查和监控
- `POST /api/worker-nodes/health-check` - 健康检查所有工作节点
- `GET /api/worker-nodes/{nodeId}/metrics` - 获取工作节点指标

### 6. 监控系统 (`/api/monitoring`)

#### 系统概览
- `GET /api/monitoring/dashboard` - 获取监控面板数据
- `GET /api/monitoring/system/overview` - 获取系统概览

#### 节点监控
- `GET /api/monitoring/nodes/overview` - 获取所有节点概览
- `GET /api/monitoring/nodes/{nodeId}` - 获取节点详细信息
- `GET /api/monitoring/nodes/{nodeId}/metrics` - 获取节点指标数据
- `GET /api/monitoring/nodes/{nodeId}/history` - 获取节点历史数据

#### 告警管理
- `GET /api/monitoring/alerts` - 获取告警列表
- `POST /api/monitoring/alerts/{alertId}/acknowledge` - 确认告警
- `POST /api/monitoring/alerts/{alertId}/resolve` - 解决告警
- `GET /api/monitoring/alerts/rules` - 获取告警规则
- `POST /api/monitoring/alerts/rules` - 创建告警规则

#### 性能分析
- `GET /api/monitoring/performance/summary` - 获取性能摘要
- `GET /api/monitoring/performance/realtime` - 获取实时性能数据

#### 系统健康和数据管理
- `GET /api/monitoring/health` - 系统健康检查
- `POST /api/monitoring/cleanup/old-data` - 清理旧数据
- `GET /api/monitoring/statistics` - 获取监控统计

### 7. 系统健康 (`/`)
- `GET /` - 后端运行状态检查

## 🆕 新增端点（已设计完成）

### 8. 管理员后台 (`/api/admin`)

#### 认证管理
- `POST /api/admin/auth/login` - 管理员登录
- `POST /api/admin/auth/logout` - 管理员登出
- `GET /api/admin/auth/check` - 检查认证状态

#### 仪表盘
- `GET /api/admin/dashboard` - 获取仪表盘数据
- `GET /api/admin/dashboard/stats` - 获取关键统计指标

#### 用户管理
- `GET /api/admin/users` - 获取用户列表（分页、搜索、筛选）
- `GET /api/admin/users/{userId}` - 获取用户详情
- `POST /api/admin/users/{userId}/disable` - 禁用用户
- `POST /api/admin/users/{userId}/enable` - 启用用户
- `POST /api/admin/users/{userId}/ban` - 封禁用户
- `POST /api/admin/users/{userId}/plan` - 更改用户计划
- `GET /api/admin/users/stats` - 获取用户统计

#### 任务管理
- `GET /api/admin/tasks` - 获取所有任务列表
- `GET /api/admin/tasks/{taskId}` - 获取任务详情
- `POST /api/admin/tasks/{taskId}/cancel` - 强制取消任务
- `POST /api/admin/tasks/{taskId}/retry` - 重试失败任务
- `GET /api/admin/tasks/queue` - 获取任务队列监控

#### 日志管理
- `GET /api/admin/logs` - 获取系统日志
- `GET /api/admin/logs/errors` - 获取错误日志
- `GET /api/admin/logs/audit` - 获取审计日志

### 9. 系统配置 (`/api/system`)

#### 系统设置
- `GET /api/system/settings` - 获取系统设置
- `POST /api/system/settings` - 更新系统设置
- `GET /api/system/settings/{key}` - 获取特定设置
- `POST /api/system/settings/{key}` - 更新特定设置

#### 功能开关
- `GET /api/system/features` - 获取功能开关列表
- `POST /api/system/features/{feature}/enable` - 启用功能
- `POST /api/system/features/{feature}/disable` - 禁用功能
- `POST /api/system/features/{feature}/toggle` - 切换功能开关

#### 限流配置
- `GET /api/system/limits` - 获取限流配置
- `POST /api/system/limits` - 更新限流配置
- `GET /api/system/limits/user` - 获取用户限制配置
- `POST /api/system/limits/user` - 更新用户限制配置

#### 维护模式
- `GET /api/system/maintenance/status` - 获取维护模式状态
- `POST /api/system/maintenance/enable` - 启用维护模式
- `POST /api/system/maintenance/disable` - 禁用维护模式

#### 系统信息
- `GET /api/system/info` - 获取系统信息
- `GET /api/system/version` - 获取版本信息
- `GET /api/system/health` - 获取系统健康状态
- `GET /api/system/status` - 获取系统状态

#### 系统操作
- `POST /api/system/cache/clear` - 清理系统缓存
- `POST /api/system/restart` - 重启系统
- `POST /api/system/backup` - 创建系统备份

### 10. 代理管理 (`/api/proxies`)

#### 代理列表管理
- `GET /api/proxies` - 获取代理列表
- `POST /api/proxies` - 添加代理
- `GET /api/proxies/{proxyId}` - 获取代理详情
- `POST /api/proxies/{proxyId}/update` - 更新代理
- `POST /api/proxies/{proxyId}/delete` - 删除代理

#### 代理状态管理
- `POST /api/proxies/{proxyId}/enable` - 启用代理
- `POST /api/proxies/{proxyId}/disable` - 禁用代理
- `POST /api/proxies/{proxyId}/test` - 测试代理连接

#### 健康检查
- `POST /api/proxies/health-check` - 批量健康检查
- `POST /api/proxies/{proxyId}/health-check` - 单个代理健康检查
- `GET /api/proxies/health/summary` - 获取健康状态摘要

#### 统计分析
- `GET /api/proxies/stats` - 获取代理使用统计
- `GET /api/proxies/{proxyId}/stats` - 获取单个代理统计
- `POST /api/proxies/stats/reset` - 重置统计数据
- `POST /api/proxies/{proxyId}/stats/reset` - 重置单个代理统计

#### 批量操作
- `POST /api/proxies/import` - 批量导入代理
- `GET /api/proxies/export` - 导出代理配置
- `POST /api/proxies/batch/enable` - 批量启用代理
- `POST /api/proxies/batch/disable` - 批量禁用代理
- `POST /api/proxies/batch/delete` - 批量删除代理

#### 代理池管理
- `GET /api/proxies/pool/status` - 获取代理池状态
- `POST /api/proxies/pool/refresh` - 刷新代理池
- `POST /api/proxies/pool/optimize` - 优化代理池

### 11. 分析统计 (`/api/analytics`)

#### 总体统计
- `GET /api/analytics/overview` - 获取总体统计概览
- `GET /api/analytics/dashboard` - 获取仪表盘统计数据

#### 用户统计
- `GET /api/analytics/users` - 获取用户统计
- `GET /api/analytics/users/growth` - 获取用户增长统计
- `GET /api/analytics/users/activity` - 获取用户活跃度统计
- `GET /api/analytics/users/retention` - 获取用户留存统计

#### 任务统计
- `GET /api/analytics/tasks` - 获取任务统计
- `GET /api/analytics/tasks/performance` - 获取任务性能统计
- `GET /api/analytics/tasks/success-rate` - 获取任务成功率统计
- `GET /api/analytics/tasks/types` - 获取任务类型统计

#### 下载统计
- `GET /api/analytics/downloads` - 获取下载统计
- `GET /api/analytics/downloads/volume` - 获取下载量统计
- `GET /api/analytics/downloads/popular` - 获取热门内容统计

#### 系统性能统计
- `GET /api/analytics/performance` - 获取系统性能统计
- `GET /api/analytics/performance/workers` - 获取工作节点性能统计
- `GET /api/analytics/performance/resources` - 获取资源使用统计

#### 趋势分析
- `GET /api/analytics/trends` - 获取趋势分析
- `GET /api/analytics/trends/forecast` - 获取预测分析

#### 报表生成
- `GET /api/analytics/reports` - 获取报表列表
- `POST /api/analytics/reports/generate` - 生成报表
- `GET /api/analytics/reports/{reportId}` - 获取报表详情
- `GET /api/analytics/reports/{reportId}/download` - 下载报表

#### 实时统计
- `GET /api/analytics/real-time` - 获取实时统计
- `GET /api/analytics/real-time/active-users` - 获取实时活跃用户
- `GET /api/analytics/real-time/tasks` - 获取实时任务统计

### 12. 通知系统 (`/api/notifications`)

#### 用户通知
- `GET /api/notifications` - 获取用户通知列表
- `GET /api/notifications/{notificationId}` - 获取通知详情
- `POST /api/notifications/{notificationId}/read` - 标记通知为已读
- `POST /api/notifications/read-all` - 标记所有通知为已读
- `POST /api/notifications/{notificationId}/delete` - 删除通知
- `POST /api/notifications/delete-all` - 删除所有通知

#### 通知统计
- `GET /api/notifications/count` - 获取通知数量统计
- `GET /api/notifications/unread-count` - 获取未读通知数量

#### 通知设置
- `GET /api/notifications/settings` - 获取通知设置
- `POST /api/notifications/settings` - 更新通知设置
- `POST /api/notifications/settings/reset` - 重置通知设置

#### 系统公告
- `GET /api/notifications/announcements` - 获取系统公告
- `GET /api/notifications/announcements/{announcementId}` - 获取公告详情
- `POST /api/notifications/announcements/{announcementId}/read` - 标记公告为已读

#### 管理员通知管理 (`/api/admin/notifications`)
- `POST /api/admin/notifications/broadcast` - 发送广播通知
- `POST /api/admin/notifications/send` - 发送通知给指定用户
- `POST /api/admin/notifications/announcements` - 创建系统公告
- `POST /api/admin/notifications/announcements/{announcementId}/update` - 更新系统公告
- `POST /api/admin/notifications/announcements/{announcementId}/delete` - 删除系统公告
- `GET /api/admin/notifications/stats` - 获取通知统计

#### 通知模板管理
- `GET /api/admin/notifications/templates` - 获取通知模板列表
- `POST /api/admin/notifications/templates` - 创建通知模板
- `POST /api/admin/notifications/templates/{templateId}/update` - 更新通知模板
- `POST /api/admin/notifications/templates/{templateId}/delete` - 删除通知模板

### 13. 内容审查 (`/api/content`)

#### 黑名单管理
- `GET /api/content/blacklist` - 获取黑名单列表
- `POST /api/content/blacklist` - 添加到黑名单
- `GET /api/content/blacklist/{blacklistId}` - 获取黑名单项详情
- `POST /api/content/blacklist/{blacklistId}/update` - 更新黑名单项
- `POST /api/content/blacklist/{blacklistId}/remove` - 从黑名单移除
- `POST /api/content/blacklist/check` - 检查内容是否在黑名单中

#### DMCA管理
- `GET /api/content/dmca` - 获取DMCA通知列表
- `POST /api/content/dmca` - 提交DMCA通知
- `GET /api/content/dmca/{dmcaId}` - 获取DMCA通知详情
- `POST /api/content/dmca/{dmcaId}/process` - 处理DMCA通知
- `POST /api/content/dmca/{dmcaId}/approve` - 批准DMCA通知
- `POST /api/content/dmca/{dmcaId}/reject` - 拒绝DMCA通知

#### 内容举报
- `GET /api/content/reports` - 获取内容举报列表
- `POST /api/content/reports` - 提交内容举报
- `GET /api/content/reports/{reportId}` - 获取举报详情
- `POST /api/content/reports/{reportId}/handle` - 处理内容举报
- `POST /api/content/reports/{reportId}/close` - 关闭内容举报

#### 内容审核
- `GET /api/content/moderation/queue` - 获取内容审核队列
- `POST /api/content/moderation/{contentId}/approve` - 批准内容
- `POST /api/content/moderation/{contentId}/reject` - 拒绝内容
- `POST /api/content/moderation/{contentId}/flag` - 标记内容

#### 内容分析
- `GET /api/content/analysis/stats` - 获取内容统计
- `GET /api/content/analysis/trends` - 获取内容趋势分析
- `POST /api/content/analysis/scan` - 扫描内容

#### 版权保护
- `GET /api/content/copyright/policies` - 获取版权政策
- `POST /api/content/copyright/check` - 检查版权
- `POST /api/content/copyright/claim` - 提交版权声明

### 14. 支付系统 (`/api/billing`)

#### 付费计划
- `GET /api/billing/plans` - 获取可用付费计划
- `GET /api/billing/plans/{planId}` - 获取计划详情
- `GET /api/billing/plans/compare` - 获取计划对比

#### 订阅管理
- `GET /api/billing/subscriptions` - 获取用户订阅
- `GET /api/billing/subscriptions/current` - 获取当前订阅
- `POST /api/billing/subscriptions/create` - 创建订阅
- `POST /api/billing/subscriptions/{subscriptionId}/upgrade` - 升级订阅
- `POST /api/billing/subscriptions/{subscriptionId}/downgrade` - 降级订阅
- `POST /api/billing/subscriptions/{subscriptionId}/cancel` - 取消订阅
- `POST /api/billing/subscriptions/{subscriptionId}/reactivate` - 重新激活订阅

#### 支付管理
- `GET /api/billing/payments` - 获取支付记录
- `GET /api/billing/payments/{paymentId}` - 获取支付详情
- `POST /api/billing/payments/create` - 创建支付
- `POST /api/billing/payments/{paymentId}/confirm` - 确认支付
- `POST /api/billing/payments/{paymentId}/refund` - 申请退款

#### 发票管理
- `GET /api/billing/invoices` - 获取发票列表
- `GET /api/billing/invoices/{invoiceId}` - 获取发票详情
- `GET /api/billing/invoices/{invoiceId}/download` - 下载发票

#### 使用量统计
- `GET /api/billing/usage` - 获取使用量统计
- `GET /api/billing/usage/current` - 获取当前使用量
- `GET /api/billing/usage/limits` - 获取使用限制

#### 优惠券管理
- `GET /api/billing/coupons` - 获取用户优惠券
- `POST /api/billing/coupons/redeem` - 兑换优惠券
- `POST /api/billing/coupons/{couponId}/apply` - 应用优惠券

#### 账单设置
- `GET /api/billing/settings` - 获取账单设置
- `POST /api/billing/settings` - 更新账单设置

## 📊 统计总结

### 端点数量统计
- **现有端点**: ~65个
- **新增端点**: ~165个
- **总计**: ~230个API端点

### 功能模块覆盖
- ✅ 用户认证和管理
- ✅ YouTube内容处理
- ✅ 任务管理和调度
- ✅ 文件下载和管理
- ✅ 工作节点监控
- ✅ 系统监控和告警
- ✅ 管理员后台管理
- ✅ 系统配置管理
- ✅ 代理池管理
- ✅ 数据分析和统计
- ✅ 通知系统
- ✅ 内容审查和版权
- ✅ 支付和订阅系统

## 🔧 技术实现状态

### 已完成
- ✅ 所有端点定义和路由配置
- ✅ 服务类占位方法
- ✅ DTO定义和数据模型
- ✅ 依赖注入配置
- ✅ 统一的响应格式和错误处理

### 待实现
- ⏳ 具体业务逻辑实现
- ⏳ 数据库操作和查询
- ⏳ 权限验证和授权
- ⏳ 输入验证和数据校验
- ⏳ 单元测试和集成测试

## 🎯 下一步计划

1. **优先级1**: 实现管理员后台核心功能
2. **优先级2**: 完善系统配置和监控
3. **优先级3**: 实现分析统计功能
4. **优先级4**: 完善通知和内容审查
5. **优先级5**: 实现支付系统功能

这个完整的API设计为前端开发提供了全面的接口支持，覆盖了项目需求文档中的所有功能模块。
