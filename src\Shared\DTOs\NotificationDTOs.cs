namespace Shared.DTOs;

// ==================== 通知管理 ====================

public record NotificationResponse(Guid Id, string Title, string Content, string Type, bool IsRead, DateTime CreatedAt, DateTime? ReadAt);

public record NotificationDetailResponse(
    Guid Id,
    string Title,
    string Content,
    string Type,
    bool IsRead,
    DateTime CreatedAt,
    DateTime? ReadAt,
    Dictionary<string, object>? Metadata);

public record NotificationCountResponse(int TotalCount, int UnreadCount, int ReadCount);

public record UnreadCountResponse(int Count);

// ==================== 通知设置 ====================

public record NotificationSettingsResponse(
    bool EmailNotifications,
    bool PushNotifications,
    bool TaskCompletionNotifications,
    bool TaskFailureNotifications,
    bool SystemNotifications,
    bool MarketingNotifications);

public record UpdateNotificationSettingsRequest(
    bool? EmailNotifications,
    bool? PushNotifications,
    bool? TaskCompletionNotifications,
    bool? TaskFailureNotifications,
    bool? SystemNotifications,
    bool? MarketingNotifications);

// ==================== 系统公告 ====================

public record AnnouncementResponse(Guid Id, string Title, string Content, string Type, string Priority, bool IsActive, DateTime CreatedAt, DateTime? ExpiresAt);

public record CreateAnnouncementRequest(string Title, string Content, string Type, string Priority, DateTime? ExpiresAt);

public record UpdateAnnouncementRequest(string? Title, string? Content, string? Type, string? Priority, bool? IsActive, DateTime? ExpiresAt);

// ==================== 管理员通知管理 ====================

public record BroadcastNotificationRequest(string Title, string Content, string Type, List<Guid>? UserIds, Dictionary<string, object>? Metadata);

public record SendNotificationRequest(List<Guid> UserIds, string Title, string Content, string Type, Dictionary<string, object>? Metadata);

public record NotificationStatsResponse(int TotalNotifications, int SentToday, int ReadToday, double ReadRate, Dictionary<string, int> NotificationsByType);

// ==================== 通知模板 ====================

public record NotificationTemplateResponse(
    Guid Id,
    string Name,
    string Type,
    string Subject,
    string Content,
    bool IsActive,
    DateTime CreatedAt,
    DateTime UpdatedAt);

public record CreateNotificationTemplateRequest(string Name, string Type, string Subject, string Content);

public record UpdateNotificationTemplateRequest(string? Name, string? Subject, string? Content, bool? IsActive);