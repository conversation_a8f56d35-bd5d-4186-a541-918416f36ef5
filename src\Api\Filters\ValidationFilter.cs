using FluentValidation;

namespace Api.Filters;

/// <summary>
///     验证过滤器 - 自动验证请求DTO
///     验证失败时抛出ValidationException，由GlobalExceptionHandler统一处理
/// </summary>
public class ValidationFilter : IEndpointFilter
{
    public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
    {
        // 遍历所有参数，查找需要验证的DTO
        for (var i = 0; i < context.Arguments.Count; i++)
        {
            var argument = context.Arguments[i];
            if (argument == null) continue;

            var argumentType = argument.GetType();

            // 跳过基础类型和系统类型
            if (IsSystemType(argumentType)) continue;

            // 查找对应的验证器
            var validatorType = typeof(IValidator<>).MakeGenericType(argumentType);
            var validator = context.HttpContext.RequestServices.GetService(validatorType) as IValidator;

            if (validator != null)
            {
                // 执行验证
                var validationResult = await validator.ValidateAsync(new ValidationContext<object>(argument));

                if (!validationResult.IsValid)
                    // 抛出验证异常，由GlobalExceptionHandler统一处理
                    throw new ValidationException(validationResult.Errors);
            }
        }

        // 验证通过，继续执行
        return await next(context);
    }

    /// <summary>
    ///     判断是否为系统类型，不需要验证
    /// </summary>
    private static bool IsSystemType(Type type)
    {
        return type.IsPrimitive || type == typeof(string) || type == typeof(DateTime) || type == typeof(DateTimeOffset) || type == typeof(TimeSpan) ||
               type == typeof(Guid) || type == typeof(decimal) || type.IsEnum || type.Namespace?.StartsWith("System") == true ||
               type.Namespace?.StartsWith("Microsoft") == true || typeof(HttpContext).IsAssignableFrom(type) ||
               typeof(CancellationToken).IsAssignableFrom(type);
    }
}

/// <summary>
///     条件验证过滤器 - 只对特定类型进行验证
///     验证失败时抛出ValidationException，由GlobalExceptionHandler统一处理
/// </summary>
/// <typeparam name="T">要验证的DTO类型</typeparam>
public class ValidationFilter<T> : IEndpointFilter where T : class
{
    public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
    {
        // 查找指定类型的参数
        var argument = context.Arguments.OfType<T>().FirstOrDefault();
        if (argument == null) return await next(context);

        // 获取验证器
        var validator = context.HttpContext.RequestServices.GetService<IValidator<T>>();
        if (validator == null) return await next(context);

        // 执行验证
        var validationResult = await validator.ValidateAsync(argument);
        if (!validationResult.IsValid)
            // 抛出验证异常，由GlobalExceptionHandler统一处理
            throw new ValidationException(validationResult.Errors);

        return await next(context);
    }
}

/// <summary>
///     验证过滤器扩展方法
/// </summary>
public static class ValidationFilterExtensions
{
    public static RouteHandlerBuilder WithValidation(this RouteHandlerBuilder builder)
    {
        return builder.AddEndpointFilter<ValidationFilter>();
    }

    public static RouteGroupBuilder WithValidation(this RouteGroupBuilder builder)
    {
        return builder.AddEndpointFilter<ValidationFilter>();
    }

    public static RouteHandlerBuilder WithValidation<TDto>(this RouteHandlerBuilder builder) where TDto : class
    {
        return builder.AddEndpointFilter<ValidationFilter<TDto>>();
    }
}