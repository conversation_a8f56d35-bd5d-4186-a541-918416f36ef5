namespace Shared.Common;

public class ApiResponse
{
    protected ApiResponse(bool success, string? message = null, string? errorCode = null, List<ValidationError>? validationErrors = null)
    {
        Success = success;
        Message = message;
        ErrorCode = errorCode;
        ValidationErrors = validationErrors;
        Timestamp = DateTime.UtcNow;
    }

    public bool Success { get; init; }
    public string? Message { get; init; }
    public string? ErrorCode { get; init; }
    public List<ValidationError>? ValidationErrors { get; init; }
    public DateTime Timestamp { get; init; }

    public static ApiResponse Ok(string? message = null)
    {
        return new ApiResponse(true, message);
    }

    public static ApiResponse Error(string errorCode, string? errorMessage = null)
    {
        var message = errorMessage ?? ErrorCodes.GetDescription(errorCode);
        return new ApiResponse(false, message, errorCode);
    }

    public static ApiResponse ValidationError(List<ValidationError> validationErrors)
    {
        ArgumentNullException.ThrowIfNull(validationErrors);
        if (validationErrors.Count == 0)
            throw new ArgumentException("验证错误不能为空", nameof(validationErrors));

        return new ApiResponse(false, ErrorCodes.GetDescription(ErrorCodes.VALIDATION_ERROR), ErrorCodes.VALIDATION_ERROR, validationErrors);
    }
}

public class ApiResponse<T> : ApiResponse
{
    private ApiResponse(bool success, T? data = default, string? message = null, string? errorCode = null, List<ValidationError>? validationErrors = null) :
        base(success, message, errorCode, validationErrors)
    {
        Data = data;
    }

    public T? Data { get; init; }

    public static ApiResponse<T> Ok(T data, string? message = null)
    {
        ArgumentNullException.ThrowIfNull(data);
        return new ApiResponse<T>(true, data, message);
    }

    public new static ApiResponse<T> Error(string errorCode, string? errorMessage = null)
    {
        var message = errorMessage ?? ErrorCodes.GetDescription(errorCode);
        return new ApiResponse<T>(false, default, message, errorCode);
    }

    public new static ApiResponse<T> ValidationError(List<ValidationError> validationErrors)
    {
        ArgumentNullException.ThrowIfNull(validationErrors);
        if (validationErrors.Count == 0)
            throw new ArgumentException("验证错误不能为空", nameof(validationErrors));

        return new ApiResponse<T>(false, default, ErrorCodes.GetDescription(ErrorCodes.VALIDATION_ERROR), ErrorCodes.VALIDATION_ERROR, validationErrors);
    }
}

public record ValidationError(string Field, string Message);