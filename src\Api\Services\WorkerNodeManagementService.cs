using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class WorkerNodeManagementService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<WorkerNodeManagementService> _logger;
    private readonly WorkerService _workerService;

    public WorkerNodeManagementService(AppDbContext dbContext, WorkerService workerService, ILogger<WorkerNodeManagementService> logger)
    {
        _dbContext = dbContext;
        _workerService = workerService;
        _logger = logger;
    }

    public async Task<ServiceResult<List<WorkerCapacityResponse>>> GetWorkerCapacityAsync()
    {
        try
        {
            var workers = await _dbContext.Workers.Where(w => w.Status == WorkerStatus.Online).ToListAsync();

            var capacityList = new List<WorkerCapacityResponse>();

            foreach (var worker in workers)
            {
                var activeTasks = await _dbContext.WorkerTasks
                    .Where(t => t.WorkerId == worker.Id && (t.Status == WorkerTaskStatus.Processing || t.Status == WorkerTaskStatus.Queued)).CountAsync();

                var latestMetrics = await _dbContext.WorkerMetrics.Where(m => m.WorkerId == worker.Id).OrderByDescending(m => m.RecordedAt)
                    .FirstOrDefaultAsync();

                var maxConcurrentTasks = CalculateMaxConcurrentTasks(worker.CpuCores, worker.TotalMemoryGB);
                var availableCapacity = Math.Max(0, maxConcurrentTasks - activeTasks);
                var canAcceptTasks = availableCapacity > 0 && worker.HealthStatus == WorkerHealthStatus.Healthy && (latestMetrics?.CpuUsagePercent ?? 0) < 80 &&
                                     (latestMetrics?.MemoryUsagePercent ?? 0) < 80;

                var capacity = new WorkerCapacityResponse(worker.Id, worker.Name, maxConcurrentTasks, activeTasks, availableCapacity,
                    latestMetrics?.CpuUsagePercent ?? 0, latestMetrics?.MemoryUsagePercent ?? 0, canAcceptTasks);

                capacityList.Add(capacity);
            }

            return ServiceResult<List<WorkerCapacityResponse>>.Success(capacityList);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作节点容量信息时发生错误");
            return ServiceResult<List<WorkerCapacityResponse>>.Failure("获取容量信息失败", "GET_CAPACITY_ERROR");
        }
    }

    public async Task<ServiceResult<List<WorkerTaskAssignmentRequest>>> GetOptimalTaskAssignmentAsync(int taskCount)
    {
        try
        {
            var capacities = await GetWorkerCapacityAsync();
            if (!capacities.IsSuccess)
                return ServiceResult<List<WorkerTaskAssignmentRequest>>.Failure(capacities.ErrorMessage!, capacities.ErrorCode);

            var availableWorkers = capacities.Data!.Where(w => w.CanAcceptTasks).OrderBy(w => w.CurrentActiveTasks).ThenByDescending(w => w.AvailableCapacity)
                .ToList();

            var assignments = new List<WorkerTaskAssignmentRequest>();

            // TODO: 实现智能任务分配算法
            // 这里应该根据工作节点的容量和当前负载来分配任务

            return ServiceResult<List<WorkerTaskAssignmentRequest>>.Success(assignments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "计算最优任务分配时发生错误");
            return ServiceResult<List<WorkerTaskAssignmentRequest>>.Failure("计算任务分配失败", "TASK_ASSIGNMENT_ERROR");
        }
    }

    public async Task<ServiceResult> RebalanceWorkloadAsync()
    {
        try
        {
            // TODO: 实现工作负载重新平衡逻辑
            await Task.Delay(1);

            _logger.LogInformation("工作负载重新平衡完成");
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新平衡工作负载时发生错误");
            return ServiceResult.Failure("重新平衡失败", "REBALANCE_ERROR");
        }
    }

    public async Task<ServiceResult> DrainWorkerNodeAsync(Guid nodeId)
    {
        try
        {
            var worker = await _dbContext.Workers.FindAsync(nodeId);
            if (worker == null) return ServiceResult.Failure("工作节点不存在", "WORKER_NOT_FOUND");

            // 设置为排空状态
            worker.Status = WorkerStatus.Draining;
            worker.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            // TODO: 实现将现有任务迁移到其他节点的逻辑

            _logger.LogInformation("工作节点开始排空: {WorkerId}", nodeId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "排空工作节点时发生错误: {WorkerId}", nodeId);
            return ServiceResult.Failure("排空工作节点失败", "DRAIN_WORKER_ERROR");
        }
    }

    public async Task<ServiceResult> ScaleWorkersAsync(int targetCount)
    {
        try
        {
            var currentCount = await _dbContext.Workers.Where(w => w.Status != WorkerStatus.Offline).CountAsync();

            if (currentCount == targetCount) return ServiceResult.Success();

            // TODO: 实现自动扩缩容逻辑
            // 这里应该根据目标数量自动启动或停止工作节点

            _logger.LogInformation("工作节点扩缩容: {Current} -> {Target}", currentCount, targetCount);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "扩缩容工作节点时发生错误");
            return ServiceResult.Failure("扩缩容失败", "SCALE_WORKERS_ERROR");
        }
    }

    private static int CalculateMaxConcurrentTasks(int cpuCores, double totalMemoryGB)
    {
        // 简单的容量计算公式：每个CPU核心可以处理2个任务，每4GB内存可以处理1个任务
        var cpuBasedCapacity = cpuCores * 2;
        var memoryBasedCapacity = (int)(totalMemoryGB / 4);

        // 取较小值作为最大并发任务数，最少为1，最多为20
        return Math.Max(1, Math.Min(20, Math.Min(cpuBasedCapacity, memoryBasedCapacity)));
    }
}