using FluentValidation;
using Shared.Common;

namespace Shared.DTOs;

public record RegisterRequest(string Email, string Password);

public class RegisterRequestValidator : AbstractValidator<RegisterRequest>
{
    public RegisterRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确").MaximumLength(255).WithMessage("邮箱长度不能超过255个字符");

        RuleFor(x => x.Password).NotEmpty().WithMessage("密码不能为空").MinimumLength(6).WithMessage("密码至少6个字符").MaximumLength(32).WithMessage("密码不能超过32个字符");
    }
}

public record LoginRequest(string Email, string Password);

public class LoginRequestValidator : AbstractValidator<LoginRequest>
{
    public LoginRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");

        RuleFor(x => x.Password).NotEmpty().WithMessage("密码不能为空");
    }
}

public record ChangePasswordRequest(string CurrentPassword, string NewPassword);

public class ChangePasswordRequestValidator : AbstractValidator<ChangePasswordRequest>
{
    public ChangePasswordRequestValidator()
    {
        RuleFor(x => x.CurrentPassword).NotEmpty().WithMessage("当前密码不能为空");

        RuleFor(x => x.NewPassword).NotEmpty().WithMessage("新密码不能为空").MinimumLength(6).WithMessage("新密码至少6个字符").MaximumLength(32).WithMessage("新密码不能超过32个字符")
            .NotEqual(x => x.CurrentPassword).WithMessage("新密码不能与当前密码相同");
    }
}

public record UpdateUserRequest(string? Email);

public class UpdateUserRequestValidator : AbstractValidator<UpdateUserRequest>
{
    public UpdateUserRequestValidator()
    {
        RuleFor(x => x.Email).EmailAddress().WithMessage("邮箱格式不正确").MaximumLength(255).WithMessage("邮箱长度不能超过255个字符").When(x => !string.IsNullOrEmpty(x.Email));
    }
}

public record ForgotPasswordRequest(string Email);

public class ForgotPasswordRequestValidator : AbstractValidator<ForgotPasswordRequest>
{
    public ForgotPasswordRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");
    }
}

public record ResetPasswordRequest(string Email, string Token, string NewPassword);

public class ResetPasswordRequestValidator : AbstractValidator<ResetPasswordRequest>
{
    public ResetPasswordRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");

        RuleFor(x => x.Token).NotEmpty().WithMessage("重置令牌不能为空");

        RuleFor(x => x.NewPassword).NotEmpty().WithMessage("新密码不能为空").MinimumLength(6).WithMessage("新密码至少6个字符").MaximumLength(32).WithMessage("新密码不能超过32个字符");
    }
}

public record VerifyEmailRequest(string Email, string Token);

public class VerifyEmailRequestValidator : AbstractValidator<VerifyEmailRequest>
{
    public VerifyEmailRequestValidator()
    {
        RuleFor(x => x.Email).NotEmpty().WithMessage("邮箱不能为空").EmailAddress().WithMessage("邮箱格式不正确");

        RuleFor(x => x.Token).NotEmpty().WithMessage("验证令牌不能为空");
    }
}

public record LoginResponse(UserResponse User, string Message);

public record UserResponse(
    Guid Id,
    UserType UserType,
    string? Email,
    UserPlanType PlanType,
    DateTime? PlanExpiresAt,
    UserAccountStatus Status,
    DateTime CreatedAt,
    DateTime? LastActiveAt);

public record CurrentUser(Guid UserId, UserType UserType, string? Email, UserPlanType PlanType, bool IsAnonymous)
{
    public bool IsPremium => PlanType == UserPlanType.Premium;
    public bool IsRegistered => UserType == UserType.Registered;
}

public record UserSessionResponse(
    Guid Id,
    string? DeviceInfo,
    string? IpAddress,
    string? UserAgent,
    bool IsCurrentSession,
    DateTime CreatedAt,
    DateTime LastAccessedAt,
    DateTime ExpiresAt);

public record UserStatsResponse(int TotalTasks, int CompletedTasks, int FailedTasks, long TotalDownloadSize, DateTime LastActiveAt);

public record RevokeSessionRequest(Guid SessionId);

public class RevokeSessionRequestValidator : AbstractValidator<RevokeSessionRequest>
{
    public RevokeSessionRequestValidator()
    {
        RuleFor(x => x.SessionId).NotEmpty().WithMessage("会话ID不能为空");
    }
}

public record UpdateUserPreferencesRequest(
    string? Language,
    string? Theme,
    bool? EmailNotifications,
    bool? PushNotifications,
    string? DefaultVideoQuality,
    string? DefaultAudioFormat);

public class UpdateUserPreferencesRequestValidator : AbstractValidator<UpdateUserPreferencesRequest>
{
    public UpdateUserPreferencesRequestValidator()
    {
        RuleFor(x => x.Language).Must(lang => lang == null || new[] { "zh-CN", "en-US" }.Contains(lang)).WithMessage("语言设置无效");

        RuleFor(x => x.Theme).Must(theme => theme == null || new[] { "light", "dark", "auto" }.Contains(theme)).WithMessage("主题设置无效");

        RuleFor(x => x.DefaultVideoQuality).Must(quality => quality == null || new[] { "720p", "1080p", "1440p", "2160p", "best" }.Contains(quality))
            .WithMessage("默认视频质量设置无效");

        RuleFor(x => x.DefaultAudioFormat).Must(format => format == null || new[] { "mp3", "aac", "opus", "best" }.Contains(format)).WithMessage("默认音频格式设置无效");
    }
}

public record UserPreferencesResponse(
    string Language,
    string Theme,
    bool EmailNotifications,
    bool PushNotifications,
    string DefaultVideoQuality,
    string DefaultAudioFormat,
    DateTime UpdatedAt);

public record UserActivityResponse(
    List<UserLoginRecord> LoginHistory,
    List<UserOperationRecord> RecentOperations,
    DateTime LastPasswordChange,
    DateTime LastEmailChange);

public record UserLoginRecord(DateTime LoginTime, string IpAddress, string UserAgent, string DeviceInfo, bool IsCurrentSession);

public record UserOperationRecord(DateTime Timestamp, string Operation, string Details, string IpAddress);