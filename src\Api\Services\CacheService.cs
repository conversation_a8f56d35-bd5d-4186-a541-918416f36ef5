using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;

namespace Api.Services;

public class CacheService(IMemoryCache memoryCache, ILogger<CacheService> logger)
{
    private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);

    public Task<T?> GetAsync<T>(string key) where T : class
    {
        try
        {
            if (memoryCache.TryGetValue(key, out var cachedValue))
            {
                if (cachedValue is string jsonString)
                {
                    var result = JsonSerializer.Deserialize<T>(jsonString);
                    logger.LogDebug("缓存命中: {Key}", key);
                    return Task.FromResult(result);
                }

                if (cachedValue is T directValue)
                {
                    logger.LogDebug("缓存命中: {Key}", key);
                    return Task.FromResult<T?>(directValue);
                }
            }

            logger.LogDebug("缓存未命中: {Key}", key);
            return Task.FromResult<T?>(null);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "获取缓存 {Key} 时发生错误", key);
            return Task.FromResult<T?>(null);
        }
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
    {
        try
        {
            var actualExpiration = expiration ?? _defaultExpiration;
            var options = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = actualExpiration,
                Priority = CacheItemPriority.Normal
            };

            var jsonString = JsonSerializer.Serialize(value);
            memoryCache.Set(key, jsonString, options);

            logger.LogDebug("已设置缓存: {Key}，过期时间: {Expiration}", key, actualExpiration);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "设置缓存 {Key} 时发生错误", key);
            return Task.CompletedTask;
        }
    }

    public void Remove(string key)
    {
        try
        {
            memoryCache.Remove(key);
            logger.LogDebug("已移除缓存: {Key}", key);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "移除缓存 {Key} 时发生错误", key);
        }
    }

    public bool Exists(string key)
    {
        return memoryCache.TryGetValue(key, out _);
    }


    public void ClearAll()
    {
        try
        {
            if (memoryCache is MemoryCache mc)
                mc.Compact(1.0);

            logger.LogInformation("已清除所有缓存");
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "清除所有缓存时发生错误");
        }
    }

    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T?>> factory, TimeSpan? expiration = null) where T : class
    {
        var cached = await GetAsync<T>(key);
        if (cached != null) return cached;

        try
        {
            var value = await factory();
            if (value != null)
                await SetAsync(key, value, expiration);
            return value;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "执行缓存工厂方法时发生错误: {Key}", key);
            return null;
        }
    }
}