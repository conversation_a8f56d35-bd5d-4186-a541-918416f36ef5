using Api.Data;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class YouTubeCacheService
{
    private readonly CacheService _cacheService;
    private readonly AppDbContext _dbContext;
    private readonly ILogger<YouTubeCacheService> _logger;

    public YouTubeCacheService(AppDbContext dbContext, CacheService cacheService, ILogger<YouTubeCacheService> logger)
    {
        _dbContext = dbContext;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<ServiceResult<YouTubeFetchStats>> GetCacheStatsAsync()
    {
        try
        {
            var videoCount = await _dbContext.YouTubeVideos.CountAsync();
            var playlistCount = await _dbContext.YouTubePlaylists.CountAsync();
            var channelCount = await _dbContext.YouTubeChannels.CountAsync();

            var expiredVideoCount = await _dbContext.YouTubeVideos.Where(v => v.ExpiresAt.HasValue && v.ExpiresAt.Value <= DateTime.UtcNow).CountAsync();

            var expiredPlaylistCount = await _dbContext.YouTubePlaylists.Where(p => p.ExpiresAt.HasValue && p.ExpiresAt.Value <= DateTime.UtcNow).CountAsync();

            var expiredChannelCount = await _dbContext.YouTubeChannels.Where(c => c.ExpiresAt.HasValue && c.ExpiresAt.Value <= DateTime.UtcNow).CountAsync();

            var stats = new YouTubeFetchStats(0, // TotalCacheHits - TODO: 实现缓存命中统计
                0, // TotalCacheMisses - TODO: 实现缓存未命中统计
                0.0, // CacheHitRate - TODO: 计算缓存命中率
                videoCount + playlistCount + channelCount // CachedItemsCount
            );

            return ServiceResult<YouTubeFetchStats>.Success(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存统计时发生错误");
            return ServiceResult<YouTubeFetchStats>.Failure("获取缓存统计失败", "CACHE_STATS_ERROR");
        }
    }

    public async Task<ServiceResult> ClearExpiredCacheAsync()
    {
        try
        {
            var now = DateTime.UtcNow;

            // 清理过期的视频缓存
            var expiredVideos = await _dbContext.YouTubeVideos.Where(v => v.ExpiresAt.HasValue && v.ExpiresAt.Value <= now).ToListAsync();

            // 清理过期的播放列表缓存
            var expiredPlaylists = await _dbContext.YouTubePlaylists.Where(p => p.ExpiresAt.HasValue && p.ExpiresAt.Value <= now).ToListAsync();

            // 清理过期的频道缓存
            var expiredChannels = await _dbContext.YouTubeChannels.Where(c => c.ExpiresAt.HasValue && c.ExpiresAt.Value <= now).ToListAsync();

            _dbContext.YouTubeVideos.RemoveRange(expiredVideos);
            _dbContext.YouTubePlaylists.RemoveRange(expiredPlaylists);
            _dbContext.YouTubeChannels.RemoveRange(expiredChannels);

            var deletedCount = await _dbContext.SaveChangesAsync();

            _logger.LogInformation("已清理 {Count} 个过期缓存项", deletedCount);

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期缓存时发生错误");
            return ServiceResult.Failure("清理缓存失败", "CACHE_CLEAR_ERROR");
        }
    }
}