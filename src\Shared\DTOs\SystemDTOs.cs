namespace Shared.DTOs;

// ==================== 系统设置 ====================

public record SystemSettingsResponse(Dictionary<string, object> Settings);

public record UpdateSystemSettingsRequest(Dictionary<string, object> Settings);

public record SettingValueResponse(string Key, object Value, string Type, string? Description);

public record UpdateSettingRequest(object Value);

// ==================== 功能开关 ====================

public record FeatureToggleResponse(string Name, bool Enabled, string Description, DateTime? LastModified);

// ==================== 限流配置 ====================

public record LimitConfigResponse(Dictionary<string, int> RateLimits, Dictionary<string, int> UserLimits, Dictionary<string, int> SystemLimits);

public record UpdateLimitConfigRequest(Dictionary<string, int> RateLimits);

public record UserLimitsResponse(Dictionary<string, int> FreeLimits, Dictionary<string, int> PremiumLimits, Dictionary<string, int> EnterpriseLimits);

public record UpdateUserLimitsRequest(Dictionary<string, int> Limits);

// ==================== 维护模式 ====================

public record MaintenanceStatusResponse(bool IsEnabled, string? Message, DateTime? StartTime, DateTime? EstimatedEndTime);

public record EnableMaintenanceRequest(string Message, DateTime? EstimatedEndTime);

// ==================== 系统信息 ====================

public record SystemInfoResponse(string Version, string Environment, DateTime StartTime, TimeSpan Uptime, SystemResourcesResponse Resources);

public record SystemResourcesResponse(double CpuUsage, long MemoryUsed, long MemoryTotal, long DiskUsed, long DiskTotal);

public record VersionInfoResponse(string Version, string BuildNumber, DateTime BuildDate, string GitCommit);

public record SystemHealthResponse(string Status, List<ComponentHealthResponse> Components);

public record ComponentHealthResponse(string Name, string Status, string? Message, DateTime LastCheck);

public record SystemStatusResponse(string Status, bool MaintenanceMode, int ActiveUsers, int ActiveTasks, int HealthyWorkers);

// ==================== 系统操作 ====================

public record BackupResponse(string BackupId, string FileName, long FileSize, DateTime CreatedAt, string DownloadUrl);