using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AnalyticsService
{
    public async Task<ServiceResult<OverviewStatsResponse>> GetOverviewStatsAsync()
    {
        await Task.Delay(1);
        return ServiceResult<OverviewStatsResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<DashboardStatsResponse>> GetDashboardStatsAsync()
    {
        await Task.Delay(1);
        return ServiceResult<DashboardStatsResponse>.Failure("功能暂未实现");
    }
}