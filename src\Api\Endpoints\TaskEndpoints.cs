using Api.Extensions;
using Api.Filters;
using Api.Middleware;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class TaskEndpoints
{
    public static void MapTaskEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/tasks").WithTags("Tasks").WithValidation();

        // ==================== 任务创建端点 ====================

        group.MapPost("/video", async Task<IResult> (CreateVideoTaskRequest request, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CreateVideoTaskAsync(request, currentUser.UserId);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, "视频下载任务创建成功") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建视频下载任务").WithDescription("创建单个视频的下载任务，支持格式选择、质量设置和时间裁剪");

        group.MapPost("/audio", async Task<IResult> (CreateAudioTaskRequest request, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CreateAudioTaskAsync(request, currentUser.UserId);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, "音频提取任务创建成功") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建音频提取任务").WithDescription("从视频中提取音频，支持多种音频格式和质量设置");

        group.MapPost("/gif", async Task<IResult> (CreateGifTaskRequest request, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CreateGifTaskAsync(request, currentUser.UserId);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, "GIF制作任务创建成功") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建GIF制作任务").WithDescription("从视频片段创建GIF动图，支持时间范围和尺寸设置");

        group.MapPost("/ringtone", async Task<IResult> (CreateRingtoneTaskRequest request, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CreateRingtoneTaskAsync(request, currentUser.UserId);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, "铃声制作任务创建成功") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建铃声制作任务").WithDescription("从视频或音频中提取片段制作手机铃声");

        group.MapPost("/subtitle", async Task<IResult> (CreateSubtitleTaskRequest request, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CreateSubtitleTaskAsync(request, currentUser.UserId);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, "字幕下载任务创建成功") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建字幕下载任务").WithDescription("下载视频的字幕文件，支持多种语言和格式");

        group.MapPost("/thumbnail", async Task<IResult> (CreateThumbnailTaskRequest request, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CreateThumbnailTaskAsync(request, currentUser.UserId);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, "缩略图下载任务创建成功") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建缩略图下载任务").WithDescription("下载视频的缩略图，支持多种尺寸选择");

        // ==================== 批量任务端点 ====================

        group.MapPost("/batch", async Task<IResult> (CreateBatchTaskRequest request, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CreateBatchTaskAsync(request, currentUser.UserId);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, "批量任务创建成功") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建批量下载任务").WithDescription("创建播放列表、频道或自定义视频列表的批量下载任务，支持选择性下载");

        // ==================== 任务查询端点 ====================

        group.MapGet("/", async Task<IResult> (TaskQueryService queryService, HttpContext context, int page = 1, int pageSize = 20,
            WorkerTaskStatus? status = null, WorkerTaskType? type = null) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await queryService.GetUserTasksWithFilterAsync(currentUser.UserId, page, pageSize, status, type);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户任务列表").WithDescription("获取当前用户的任务列表，支持分页和状态、类型筛选");

        group.MapGet("/{taskId}", async Task<IResult> (Guid taskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.GetTaskAsync(taskId, currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务详情").WithDescription("获取指定任务的详细信息，包括进度和结果文件");

        group.MapGet("/batch", async Task<IResult> (TaskQueryService queryService, HttpContext context,
            int page = 1, int pageSize = 20, BatchTaskStatus? status = null) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await queryService.GetUserBatchTasksAsync(currentUser.UserId, page, pageSize, status);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户批量任务列表").WithDescription("获取当前用户的批量任务列表，支持分页和状态筛选");

        group.MapGet("/batch/{batchTaskId}", async Task<IResult> (Guid batchTaskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.GetBatchTaskAsync(batchTaskId, currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取批量任务详情").WithDescription("获取指定批量任务的详细信息和所有子任务状态");

        group.MapGet("/statistics", async Task<IResult> (TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.GetUserTaskStatsAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务统计").WithDescription("获取当前用户的任务统计信息，包括各状态任务数量");

        // ==================== 任务操作端点 ====================

        group.MapPost("/{taskId}/cancel", async Task<IResult> (Guid taskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CancelTaskAsync(taskId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务已取消");
        }).WithSummary("取消任务").WithDescription("取消指定的任务，仅能取消待处理或进行中的任务");

        group.MapPost("/{taskId}/retry", async Task<IResult> (Guid taskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.RetryTaskAsync(taskId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务重试成功");
        }).WithSummary("重试任务").WithDescription("重试失败的任务，将任务重新加入处理队列");

        group.MapPost("/{taskId}/delete", async Task<IResult> (Guid taskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.DeleteCompletedTaskAsync(taskId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务删除成功");
        }).WithSummary("删除任务").WithDescription("删除已完成或已取消的任务及其相关文件");

        group.MapPost("/batch/{batchTaskId}/cancel", async Task<IResult> (Guid batchTaskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CancelBatchTaskAsync(batchTaskId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "批量任务已取消");
        }).WithSummary("取消批量任务").WithDescription("取消指定的批量任务及其所有子任务");

        group.MapPost("/batch/{batchTaskId}/pause", async Task<IResult> (Guid batchTaskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.PauseBatchTaskAsync(batchTaskId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "批量任务已暂停");
        }).WithSummary("暂停批量任务").WithDescription("暂停正在运行的批量任务");

        group.MapPost("/batch/{batchTaskId}/resume", async Task<IResult> (Guid batchTaskId, TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.ResumeBatchTaskAsync(batchTaskId, currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "批量任务已恢复");
        }).WithSummary("恢复批量任务").WithDescription("恢复已暂停的批量任务");

        // ==================== 批量操作端点 ====================

        group.MapPost("/cleanup", async Task<IResult> (TaskService taskService, HttpContext context, int daysOld = 7) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CleanupCompletedTasksAsync(currentUser.UserId, daysOld);
            return result.IsSuccess ? ResultExtensions.ApiOk(result.Data!, $"清理了 {result.Data} 个已完成任务") : ResultExtensions.ToHttpResult(result);
        }).WithSummary("清理已完成任务").WithDescription("清理指定天数前的已完成任务，释放存储空间");

        group.MapPost("/cancel-all", async Task<IResult> (TaskService taskService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await taskService.CancelAllUserTasksAsync(currentUser.UserId);
            return ResultExtensions.ToHttpResultOrOk(result, "所有任务已取消");
        }).WithSummary("取消所有任务").WithDescription("取消当前用户的所有待处理和进行中的任务");
    }
}