using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class NotificationService
{
    public async Task<ServiceResult<PagedResponse<NotificationResponse>>> GetUserNotificationsAsync(Guid userId, int page, int pageSize, bool? isRead,
        string? type)
    {
        await Task.Delay(1);
        return ServiceResult<PagedResponse<NotificationResponse>>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<NotificationDetailResponse>> GetNotificationDetailAsync(Guid notificationId, Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult<NotificationDetailResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> MarkAsReadAsync(Guid notificationId, Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> MarkAllAsReadAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> DeleteNotificationAsync(Guid notificationId, Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> DeleteAllNotificationsAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<NotificationCountResponse>> GetNotificationCountAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult<NotificationCountResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<UnreadCountResponse>> GetUnreadCountAsync(Guid userId)
    {
        await Task.Delay(1);
        return ServiceResult<UnreadCountResponse>.Failure("功能暂未实现");
    }
}