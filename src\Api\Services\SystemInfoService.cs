using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class SystemInfoService
{
    public async Task<ServiceResult<SystemInfoResponse>> GetSystemInfoAsync()
    {
        await Task.Delay(1);
        return ServiceResult<SystemInfoResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<VersionInfoResponse>> GetVersionInfoAsync()
    {
        await Task.Delay(1);
        return ServiceResult<VersionInfoResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<SystemHealthResponse>> GetSystemHealthAsync()
    {
        await Task.Delay(1);
        return ServiceResult<SystemHealthResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<SystemStatusResponse>> GetSystemStatusAsync()
    {
        await Task.Delay(1);
        return ServiceResult<SystemStatusResponse>.Failure("功能暂未实现");
    }
}