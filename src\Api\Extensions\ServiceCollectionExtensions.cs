using Api.Data;
using Api.Scheduled;
using Api.Services;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Shared.DTOs;

namespace Api.Extensions;

/// <summary>
///     服务注册扩展方法 - 解决Program.cs中手动注册大量服务的问题
///     按功能模块组织服务注册，提高可维护性
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    ///     注册数据库相关服务
    /// </summary>
    public static IServiceCollection AddDatabaseServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册DbContext
        services.AddDbContext<AppDbContext>(options => options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        return services;
    }

    /// <summary>
    ///     注册验证器
    /// </summary>
    public static IServiceCollection AddValidationServices(this IServiceCollection services)
    {
        // 自动注册所有验证器
        services.AddValidatorsFromAssemblyContaining<CreateVideoTaskRequestValidator>();

        // 也可以手动注册特定验证器
        services.AddScoped<IValidator<CreateVideoTaskRequest>, CreateVideoTaskRequestValidator>();
        services.AddScoped<IValidator<CreateAudioTaskRequest>, CreateAudioTaskRequestValidator>();
        services.AddScoped<IValidator<CreateGifTaskRequest>, CreateGifTaskRequestValidator>();
        services.AddScoped<IValidator<CreateBatchTaskRequest>, CreateBatchTaskRequestValidator>();
        services.AddScoped<IValidator<TaskProgressUpdateRequest>, TaskProgressUpdateRequestValidator>();
        services.AddScoped<IValidator<TaskCompletionRequest>, TaskCompletionRequestValidator>();

        return services;
    }

    /// <summary>
    ///     注册业务服务
    /// </summary>
    public static IServiceCollection AddBusinessServices(this IServiceCollection services)
    {
        // 用户相关服务
        services.AddScoped<AuthService>();
        services.AddScoped<UserService>();

        // 任务相关服务
        services.AddScoped<TaskPublishService>();

        // YouTube相关服务
        services.AddScoped<YouTubeService>();

        // 工作节点相关服务
        services.AddScoped<WorkerService>();
        services.AddScoped<ProxyService>();

        // 文件相关服务
        services.AddScoped<FileDownloadService>();

        // 监控相关服务
        services.AddScoped<HealthCheckService>();
        services.AddScoped<MonitoringDataService>();

        // 缓存服务
        services.AddScoped<CacheService>();

        // 邮件服务
        services.AddScoped<EmailService>();

        return services;
    }


    /// <summary>
    ///     注册后台服务
    /// </summary>
    public static IServiceCollection AddBackgroundServices(this IServiceCollection services)
    {
        // 注册所有后台服务
        services.AddHostedService<CleanupService>();
        services.AddHostedService<ProxyHealthCheckService>();
        services.AddHostedService<WorkerNodeHealthCheckService>();
        services.AddHostedService<WorkerNodeMonitoringService>();
        services.AddHostedService<TaskSchedulerService>();

        return services;
    }

    /// <summary>
    ///     注册HTTP客户端
    /// </summary>
    public static IServiceCollection AddHttpClients(this IServiceCollection services)
    {
        // 为特定服务配置HTTP客户端
        services.AddHttpClient<WorkerService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(60);
            client.DefaultRequestHeaders.Add("User-Agent", "YTDownloader-Api/1.0");
        });

        services.AddHttpClient<YouTubeService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "YTDownloader-Api/1.0");
        });

        return services;
    }

    /// <summary>
    ///     注册缓存服务
    /// </summary>
    public static IServiceCollection AddCachingServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 内存缓存
        services.AddMemoryCache();

        return services;
    }

    /// <summary>
    ///     注册CORS服务
    /// </summary>
    public static IServiceCollection AddCorsServices(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    /// <summary>
    ///     注册认证和授权服务
    /// </summary>
    public static IServiceCollection AddAuthenticationServices(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

    /// <summary>
    ///     注册API文档服务
    /// </summary>
    public static IServiceCollection AddApiDocumentationServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddOpenApi();

        return services;
    }

    /// <summary>
    ///     一键注册所有服务 - 简化Program.cs
    /// </summary>
    public static IServiceCollection AddApplicationServices(this IServiceCollection services, IConfiguration configuration)
    {
        return services.AddDatabaseServices(configuration).AddValidationServices().AddBusinessServices().AddBackgroundServices().AddHttpClients()
            .AddCachingServices(configuration).AddCorsServices(configuration).AddAuthenticationServices(configuration)
            .AddApiDocumentationServices(configuration);
    }
}