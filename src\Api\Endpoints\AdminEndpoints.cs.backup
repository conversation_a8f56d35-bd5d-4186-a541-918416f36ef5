using Api.Extensions;
using Api.Filters;
using Api.Middleware;
using Api.Services;
using Shared.Common;
using Shared.DTOs;

namespace Api.Endpoints;

public static class AdminEndpoints
{
    public static void MapAdminEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin").WithTags("Admin").WithValidation();

        group.MapPost("/auth/login", async Task<IResult> (AdminLoginRequest request, AdminAuthService adminAuthService) =>
        {
            var result = await adminAuthService.LoginAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("管理员登录").AllowAnonymous();

        group.MapPost("/auth/logout", async Task<IResult> (AdminAuthService adminAuthService, HttpContext context) =>
        {
            var result = await adminAuthService.LogoutAsync(context);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("管理员登出");

        group.MapGet("/auth/check", async Task<IResult> (AdminAuthService adminAuthService, HttpContext context) =>
        {
            var result = await adminAuthService.CheckAuthStatusAsync(context);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("检查管理员认证状态");

        group.MapGet("/dashboard", async Task<IResult> (AdminDashboardService dashboardService) =>
        {
            var result = await dashboardService.GetDashboardDataAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取管理员仪表盘数据");

        group.MapGet("/dashboard/stats", async Task<IResult> (AdminDashboardService dashboardService) =>
        {
            var result = await dashboardService.GetKeyStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取关键统计指标");

        // ==================== 用户管理端点 ====================

        group.MapGet("/users", async Task<IResult> (AdminUserService userService, int page = 1, int pageSize = 20, string? search = null,
            UserType? userType = null, UserAccountStatus? status = null) =>
        {
            var result = await userService.GetUsersAsync(page, pageSize, search, userType, status);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户列表");

        group.MapGet("/users/{userId}", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.GetUserDetailAsync(userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户详情");

        group.MapPost("/users/{userId}/disable", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.DisableUserAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户已禁用");
        }).WithSummary("禁用用户");

        group.MapPost("/users/{userId}/enable", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.EnableUserAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户已启用");
        }).WithSummary("启用用户");

        

        group.MapPost("/users/{userId}/plan", async Task<IResult> (Guid userId, UpdateUserPlanRequest request, AdminUserService userService) =>
        {
            var result = await userService.UpdateUserPlanAsync(userId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "用户计划已更新");
        }).WithSummary("更改用户计划");

        group.MapPost("/users/{userId}/delete", async Task<IResult> (Guid userId, AdminUserService userService) =>
        {
            var result = await userService.DeleteUserAsync(userId);
            return ResultExtensions.ToHttpResultOrOk(result, "用户已删除");
        }).WithSummary("删除用户");

        group.MapGet("/users/stats", async Task<IResult> (AdminUserService userService) =>
        {
            var result = await userService.GetUserStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户统计信息");

        // ==================== 任务管理端点 ====================

        group.MapGet("/tasks", async Task<IResult> (AdminTaskService taskService, int page = 1, int pageSize = 20, WorkerTaskStatus? status = null,
            WorkerTaskType? type = null, Guid? userId = null) =>
        {
            var result = await taskService.GetAllTasksAsync(page, pageSize, status, type, userId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取所有任务列表");

        group.MapGet("/tasks/{taskId}", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.GetTaskDetailAsync(taskId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务详情");

        group.MapPost("/tasks/{taskId}/cancel", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.ForceCancelTaskAsync(taskId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务已强制取消");
        }).WithSummary("强制取消任务");

        group.MapPost("/tasks/{taskId}/retry", async Task<IResult> (Guid taskId, AdminTaskService taskService) =>
        {
            var result = await taskService.RetryFailedTaskAsync(taskId);
            return ResultExtensions.ToHttpResultOrOk(result, "任务已重试");
        }).WithSummary("重试失败任务");

        group.MapGet("/tasks/queue", async Task<IResult> (AdminTaskService taskService) =>
        {
            var result = await taskService.GetTaskQueueStatusAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务队列监控");

        // ==================== YouTube 管理端点 ====================

        group.MapGet("/youtube/cache/stats", async Task<IResult> (YouTubeCacheService cacheService) =>
        {
            var result = await cacheService.GetCacheStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取YouTube缓存统计");

        group.MapPost("/youtube/cache/clear", async Task<IResult> (YouTubeCacheService cacheService) =>
        {
            var result = await cacheService.ClearExpiredCacheAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "缓存清理完成");
        }).WithSummary("清理过期缓存");

        group.MapGet("/youtube/health", async Task<IResult> (WorkerService workerService, ProxyService proxyService) =>
        {
            var workerStats = await workerService.GetWorkerNodeStatsAsync();
            var proxyStats = await proxyService.GetProxyStatsAsync();
            var healthStatus = new
            {
                WorkerNodes = workerStats.IsSuccess ? workerStats.Data : null,
                ProxyStats = proxyStats.IsSuccess ? proxyStats.Data : null,
                Timestamp = DateTime.UtcNow
            };
            return ResultExtensions.ApiOk(healthStatus);
        }).WithSummary("YouTube服务健康检查");

        // ==================== 日志管理端点 ====================

        group.MapGet("/logs", async Task<IResult> (AdminLogService logService, int page = 1, int pageSize = 50, string? level = null,
            DateTime? startTime = null, DateTime? endTime = null) =>
        {
            var result = await logService.GetSystemLogsAsync(page, pageSize, level, startTime, endTime);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统日志");

        group.MapGet("/logs/errors", async Task<IResult> (AdminLogService logService,
            int page = 1, int pageSize = 50, DateTime? startTime = null, DateTime? endTime = null) =>
        {
            var result = await logService.GetErrorLogsAsync(page, pageSize, startTime, endTime);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取错误日志");

        group.MapGet("/logs/audit", async Task<IResult> (AdminLogService logService, int page = 1, int pageSize = 50, string? action = null,
            Guid? userId = null, DateTime? startTime = null, DateTime? endTime = null) =>
        {
            var result = await logService.GetAuditLogsAsync(page, pageSize, action, userId, startTime, endTime);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取审计日志");
    }

    public static void MapAdminContentEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/content").WithTags("Admin Content Management").WithValidation();

        // ==================== 黑名单管理端点 ====================

        group.MapGet("/blacklist", async Task<IResult> (ContentModerationService moderationService,
            int page = 1, int pageSize = 20, string? type = null, string? status = null) =>
        {
            var result = await moderationService.GetBlacklistAsync(page, pageSize, type, status);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取黑名单列表");

        group.MapPost("/blacklist", async Task<IResult> (AddToBlacklistRequest request, ContentModerationService moderationService) =>
        {
            var result = await moderationService.AddToBlacklistAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("添加到黑名单");

        group.MapGet("/blacklist/{blacklistId}", async Task<IResult> (Guid blacklistId, ContentModerationService moderationService) =>
        {
            var result = await moderationService.GetBlacklistItemAsync(blacklistId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取黑名单项详情");

        group.MapPost("/blacklist/{blacklistId}/update",
            async Task<IResult> (Guid blacklistId, UpdateBlacklistRequest request, ContentModerationService moderationService) =>
            {
                var result = await moderationService.UpdateBlacklistItemAsync(blacklistId, request);
                return ResultExtensions.ToHttpResultOrOk(result, "黑名单项已更新");
            }).WithSummary("更新黑名单项");

        group.MapPost("/blacklist/{blacklistId}/remove", async Task<IResult> (Guid blacklistId, ContentModerationService moderationService) =>
        {
            var result = await moderationService.RemoveFromBlacklistAsync(blacklistId);
            return ResultExtensions.ToHttpResultOrOk(result, "已从黑名单移除");
        }).WithSummary("从黑名单移除");

        group.MapPost("/blacklist/check", async Task<IResult> (CheckBlacklistRequest request, ContentModerationService moderationService) =>
        {
            var result = await moderationService.CheckBlacklistAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("检查内容是否在黑名单中");

        // ==================== DMCA管理端点 ====================

        group.MapGet("/dmca", async Task<IResult> (DmcaService dmcaService, int page = 1, int pageSize = 20, string? status = null, DateTime? startDate = null,
            DateTime? endDate = null) =>
        {
            var result = await dmcaService.GetDmcaNoticesAsync(page, pageSize, status, startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取DMCA通知列表");

        group.MapPost("/dmca", async Task<IResult> (SubmitDmcaNoticeRequest request, DmcaService dmcaService) =>
        {
            var result = await dmcaService.SubmitDmcaNoticeAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("提交DMCA通知");

        group.MapGet("/dmca/{dmcaId}", async Task<IResult> (Guid dmcaId, DmcaService dmcaService) =>
        {
            var result = await dmcaService.GetDmcaNoticeDetailAsync(dmcaId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取DMCA通知详情");

        group.MapPost("/dmca/{dmcaId}/process", async Task<IResult> (Guid dmcaId, ProcessDmcaNoticeRequest request, DmcaService dmcaService) =>
        {
            var result = await dmcaService.ProcessDmcaNoticeAsync(dmcaId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "DMCA通知已处理");
        }).WithSummary("处理DMCA通知");

        group.MapPost("/dmca/{dmcaId}/approve", async Task<IResult> (Guid dmcaId, DmcaService dmcaService) =>
        {
            var result = await dmcaService.ApproveDmcaNoticeAsync(dmcaId);
            return ResultExtensions.ToHttpResultOrOk(result, "DMCA通知已批准");
        }).WithSummary("批准DMCA通知");

        group.MapPost("/dmca/{dmcaId}/reject", async Task<IResult> (Guid dmcaId, RejectDmcaNoticeRequest request, DmcaService dmcaService) =>
        {
            var result = await dmcaService.RejectDmcaNoticeAsync(dmcaId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "DMCA通知已拒绝");
        }).WithSummary("拒绝DMCA通知");

        // ==================== 内容举报端点 ====================

        group.MapGet("/reports", async Task<IResult> (ContentReportService reportService, int page = 1, int pageSize = 20, string? status = null,
            string? type = null, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await reportService.GetContentReportsAsync(page, pageSize, status, type, startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取内容举报列表");

        group.MapPost("/reports", async Task<IResult> (SubmitContentReportRequest request, ContentReportService reportService, HttpContext context) =>
        {
            var currentUser = context.GetCurrentUser();
            if (currentUser == null) return ResultExtensions.ApiUnauthorized();
            var result = await reportService.SubmitContentReportAsync(request, currentUser.UserId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("提交内容举报");

        group.MapGet("/reports/{reportId}", async Task<IResult> (Guid reportId, ContentReportService reportService) =>
        {
            var result = await reportService.GetContentReportDetailAsync(reportId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取举报详情");

        group.MapPost("/reports/{reportId}/handle",
            async Task<IResult> (Guid reportId, HandleContentReportRequest request, ContentReportService reportService) =>
            {
                var result = await reportService.HandleContentReportAsync(reportId, request);
                return ResultExtensions.ToHttpResultOrOk(result, "举报已处理");
            }).WithSummary("处理内容举报");

        group.MapPost("/reports/{reportId}/close", async Task<IResult> (Guid reportId, CloseContentReportRequest request, ContentReportService reportService) =>
        {
            var result = await reportService.CloseContentReportAsync(reportId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "举报已关闭");
        }).WithSummary("关闭内容举报");

        // ==================== 内容审核端点 ====================

        group.MapGet("/moderation/queue", async Task<IResult> (ContentModerationService moderationService,
            int page = 1, int pageSize = 20, string? priority = null) =>
        {
            var result = await moderationService.GetModerationQueueAsync(page, pageSize, priority);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取内容审核队列");

        group.MapPost("/moderation/{contentId}/approve", async Task<IResult> (Guid contentId, ContentModerationService moderationService) =>
        {
            var result = await moderationService.ApproveContentAsync(contentId);
            return ResultExtensions.ToHttpResultOrOk(result, "内容已批准");
        }).WithSummary("批准内容");

        group.MapPost("/moderation/{contentId}/reject",
            async Task<IResult> (Guid contentId, RejectContentRequest request, ContentModerationService moderationService) =>
            {
                var result = await moderationService.RejectContentAsync(contentId, request);
                return ResultExtensions.ToHttpResultOrOk(result, "内容已拒绝");
            }).WithSummary("拒绝内容");

        group.MapPost("/moderation/{contentId}/flag",
            async Task<IResult> (Guid contentId, FlagContentRequest request, ContentModerationService moderationService) =>
            {
                var result = await moderationService.FlagContentAsync(contentId, request);
                return ResultExtensions.ToHttpResultOrOk(result, "内容已标记");
            }).WithSummary("标记内容");
        

        // ==================== 版权保护端点 ====================

        group.MapGet("/copyright/policies", async Task<IResult> (CopyrightService copyrightService) =>
        {
            var result = await copyrightService.GetCopyrightPoliciesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取版权政策");

        group.MapPost("/copyright/check", async Task<IResult> (CheckCopyrightRequest request, CopyrightService copyrightService) =>
        {
            var result = await copyrightService.CheckCopyrightAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("检查版权");

        group.MapPost("/copyright/claim", async Task<IResult> (SubmitCopyrightClaimRequest request, CopyrightService copyrightService) =>
        {
            var result = await copyrightService.SubmitCopyrightClaimAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("提交版权声明");
    }


    public static void MapAdminAnalyticsEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/admin/analytics").WithTags("Admin Analytics").WithValidation();

        // ==================== 总体统计端点 ====================

        group.MapGet("/overview", async Task<IResult> (AnalyticsService analyticsService) =>
        {
            var result = await analyticsService.GetOverviewStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取总体统计概览");

        group.MapGet("/dashboard", async Task<IResult> (AnalyticsService analyticsService) =>
        {
            var result = await analyticsService.GetDashboardStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取仪表盘统计数据");

        // ==================== 用户统计端点 ====================

        group.MapGet("/users", async Task<IResult> (UserAnalyticsService userAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await userAnalyticsService.GetUserStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户统计");

        group.MapGet("/users/growth", async Task<IResult> (UserAnalyticsService userAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await userAnalyticsService.GetUserGrowthStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户增长统计");

        group.MapGet("/users/activity", async Task<IResult> (UserAnalyticsService userAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await userAnalyticsService.GetUserActivityStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户活跃度统计");

        group.MapGet("/users/retention",
            async Task<IResult> (UserAnalyticsService userAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
            {
                var result = await userAnalyticsService.GetUserRetentionStatsAsync(startDate, endDate);
                return ResultExtensions.ToHttpResult(result);
            }).WithSummary("获取用户留存统计");

        // ==================== 任务统计端点 ====================

        group.MapGet("/tasks", async Task<IResult> (TaskAnalyticsService taskAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await taskAnalyticsService.GetTaskStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务统计");

        group.MapGet("/tasks/performance",
            async Task<IResult> (TaskAnalyticsService taskAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
            {
                var result = await taskAnalyticsService.GetTaskPerformanceStatsAsync(startDate, endDate);
                return ResultExtensions.ToHttpResult(result);
            }).WithSummary("获取任务性能统计");

        group.MapGet("/tasks/success-rate", async Task<IResult> (TaskAnalyticsService taskAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await taskAnalyticsService.GetTaskSuccessRateStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务成功率统计");

        group.MapGet("/tasks/types", async Task<IResult> (TaskAnalyticsService taskAnalyticsService, DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await taskAnalyticsService.GetTaskTypeStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取任务类型统计");

        // ==================== 下载统计端点 ====================

        group.MapGet("/downloads", async Task<IResult> (DownloadAnalyticsService downloadAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, string? groupBy = "day") =>
        {
            var result = await downloadAnalyticsService.GetDownloadStatsAsync(startDate, endDate, groupBy);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取下载统计");

        group.MapGet("/downloads/volume", async Task<IResult> (DownloadAnalyticsService downloadAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await downloadAnalyticsService.GetDownloadVolumeStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取下载量统计");

        group.MapGet("/downloads/popular", async Task<IResult> (DownloadAnalyticsService downloadAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null, int limit = 10) =>
        {
            var result = await downloadAnalyticsService.GetPopularContentStatsAsync(startDate, endDate, limit);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取热门内容统计");

        // ==================== 系统性能统计端点 ====================

        group.MapGet("/performance", async Task<IResult> (PerformanceAnalyticsService performanceAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await performanceAnalyticsService.GetPerformanceStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统性能统计");

        group.MapGet("/performance/workers", async Task<IResult> (PerformanceAnalyticsService performanceAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await performanceAnalyticsService.GetWorkerPerformanceStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取工作节点性能统计");

        group.MapGet("/performance/resources", async Task<IResult> (PerformanceAnalyticsService performanceAnalyticsService,
            DateTime? startDate = null, DateTime? endDate = null) =>
        {
            var result = await performanceAnalyticsService.GetResourceUsageStatsAsync(startDate, endDate);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取资源使用统计");

        // ==================== 实时统计端点 ====================

        group.MapGet("/real-time", async Task<IResult> (RealTimeAnalyticsService realTimeService) =>
        {
            var result = await realTimeService.GetRealTimeStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取实时统计");

        group.MapGet("/real-time/active-users", async Task<IResult> (RealTimeAnalyticsService realTimeService) =>
        {
            var result = await realTimeService.GetActiveUsersAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取实时活跃用户");

        group.MapGet("/real-time/tasks", async Task<IResult> (RealTimeAnalyticsService realTimeService) =>
        {
            var result = await realTimeService.GetRealTimeTaskStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取实时任务统计");
    }
}