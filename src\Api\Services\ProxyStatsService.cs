using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class ProxyStatsService
{
    public async Task<ServiceResult<ProxyStatsResponse>> GetProxyStatsAsync()
    {
        await Task.Delay(1);
        return ServiceResult<ProxyStatsResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<ProxyDetailStatsResponse>> GetProxyDetailStatsAsync(Guid proxyId)
    {
        await Task.Delay(1);
        return ServiceResult<ProxyDetailStatsResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> ResetAllStatsAsync()
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> ResetProxyStatsAsync(Guid proxyId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }
}