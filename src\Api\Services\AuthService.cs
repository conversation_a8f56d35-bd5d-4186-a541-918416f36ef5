﻿using System.Security.Cryptography;
using Api.Data;
using Api.Data.Models;
using Microsoft.EntityFrameworkCore;
using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AuthService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly ILogger<AuthService> _logger;

    public AuthService(AppDbContext dbContext, ILogger<AuthService> logger, EmailService emailService)
    {
        _dbContext = dbContext;
        _logger = logger;
        _emailService = emailService;
    }

    public async Task<ServiceResult<UserResponse>> RegisterAsync(RegisterRequest request, Guid? anonymousUserId = null)
    {
        try
        {
            // 检查邮箱是否已存在
            var existingUser = await _dbContext.Users.Where(u => u.Email == request.Email.ToLower() && u.UserType == UserType.Registered).FirstOrDefaultAsync();
            if (existingUser != null) return ServiceResult<UserResponse>.Failure("该邮箱已被注册", ErrorCodes.USER_EMAIL_EXISTS);

            // 创建新用户
            var newUser = new User
            {
                Id = Guid.NewGuid(),
                UserType = UserType.Registered,
                Email = request.Email.ToLower(),
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                PlanType = UserPlanType.Free,
                Status = UserAccountStatus.Active,
                CreatedAt = DateTime.UtcNow,
                LastActiveAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            _dbContext.Users.Add(newUser);

            // 如果有匿名用户ID，需要迁移数据
            if (anonymousUserId.HasValue) await MigrateAnonymousUserDataAsync(anonymousUserId.Value, newUser.Id);

            await _dbContext.SaveChangesAsync();

            // 发送邮箱验证邮件
            try
            {
                var verificationToken = await CreateEmailVerificationTokenAsync(newUser.Id);
                await _emailService.SendEmailVerificationAsync(newUser.Email!, verificationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "向邮箱 {Email} 发送验证邮件失败", request.Email);
            }

            _logger.LogInformation("用户注册成功: {Email}", request.Email);

            var userResponse = MapToUserResponse(newUser);
            return ServiceResult<UserResponse>.Success(userResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户 {Email} 注册时发生错误", request.Email);
            return ServiceResult<UserResponse>.Failure("注册失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<LoginResponse>> LoginAsync(LoginRequest request, Guid? anonymousUserId = null)
    {
        try
        {
            var user = await _dbContext.Users.Where(u => u.Email == request.Email.ToLower() && u.UserType == UserType.Registered).FirstOrDefaultAsync();
            if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
                return ServiceResult<LoginResponse>.Failure("邮箱或密码错误");

            if (user.Status != UserAccountStatus.Active) return ServiceResult<LoginResponse>.Failure("账户已被禁用，请联系客服");

            // 如果有匿名用户ID，需要迁移数据
            if (anonymousUserId.HasValue) await MigrateAnonymousUserDataAsync(anonymousUserId.Value, user.Id);

            // 更新最后活跃时间
            user.LastActiveAt = DateTime.UtcNow;
            user.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("用户登录成功: {Email}", request.Email);

            var userResponse = MapToUserResponse(user);
            var loginResponse = new LoginResponse(userResponse, "登录成功");

            return ServiceResult<LoginResponse>.Success(loginResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户 {Email} 登录时发生错误", request.Email);
            return ServiceResult<LoginResponse>.Failure("登录失败，请稍后重试");
        }
    }

    public async Task<ServiceResult> ChangePasswordAsync(Guid userId, ChangePasswordRequest request)
    {
        try
        {
            var user = await _dbContext.Users.Where(u => u.Id == userId && u.UserType == UserType.Registered && u.Status == UserAccountStatus.Active)
                .FirstOrDefaultAsync();
            if (user == null) return ServiceResult.Failure("用户不存在");

            if (!BCrypt.Net.BCrypt.Verify(request.CurrentPassword, user.PasswordHash)) return ServiceResult.Failure("当前密码错误");

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();

            _logger.LogInformation("用户 {UserId} 密码修改成功", userId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户 {UserId} 修改密码时发生错误", userId);
            return ServiceResult.Failure("修改密码失败，请稍后重试");
        }
    }

    public async Task<ServiceResult<UserResponse>> GetUserAsync(Guid userId)
    {
        try
        {
            var user = await _dbContext.Users.Where(u => u.Id == userId && u.Status == UserAccountStatus.Active).FirstOrDefaultAsync();
            if (user == null) return ServiceResult<UserResponse>.Failure("用户不存在");
            var userResponse = MapToUserResponse(user);
            return ServiceResult<UserResponse>.Success(userResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户 {UserId} 信息时发生错误", userId);
            return ServiceResult<UserResponse>.Failure("获取用户信息失败");
        }
    }

    private async Task MigrateAnonymousUserDataAsync(Guid anonymousUserId, Guid registeredUserId)
    {
        try
        {
            // 迁移WorkerTasks
            await _dbContext.WorkerTasks.Where(wt => wt.UserId == anonymousUserId).ExecuteUpdateAsync(wt => wt.SetProperty(x => x.UserId, registeredUserId));

            // 迁移BatchTasks
            await _dbContext.BatchTasks.Where(bt => bt.UserId == anonymousUserId).ExecuteUpdateAsync(bt => bt.SetProperty(x => x.UserId, registeredUserId));

            // 删除匿名用户记录
            await _dbContext.Users.Where(u => u.Id == anonymousUserId && u.UserType == UserType.Anonymous).ExecuteDeleteAsync();

            _logger.LogInformation("已迁移匿名用户数据: {AnonymousUserId} -> {RegisteredUserId}", anonymousUserId, registeredUserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "迁移匿名用户数据时发生错误: {AnonymousUserId} -> {RegisteredUserId}", anonymousUserId, registeredUserId);
        }
    }

    public async Task<ServiceResult> VerifyEmailAsync(VerifyEmailRequest request)
    {
        try
        {
            var user = await ValidateEmailVerificationTokenAsync(request.Email, request.Token);
            if (user == null) return ServiceResult.Failure("验证链接无效或已过期");

            user.EmailVerified = true;
            user.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            await ClearEmailVerificationTokenAsync(user.Id);

            _logger.LogInformation("用户 {UserId} 邮箱验证成功", user.Id);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证邮箱 {Email} 时发生错误", request.Email);
            return ServiceResult.Failure("邮箱验证失败，请稍后重试");
        }
    }

    public async Task<ServiceResult> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            var user = await _dbContext.Users
                .Where(u => u.Email == request.Email.ToLower() && u.UserType == UserType.Registered && u.Status == UserAccountStatus.Active)
                .FirstOrDefaultAsync();
            if (user == null)
                // 为了安全，即使用户不存在也返回成功
                return ServiceResult.Success();

            var resetToken = await CreatePasswordResetTokenAsync(user.Id);
            await _emailService.SendPasswordResetAsync(user.Email!, resetToken);

            _logger.LogInformation("已向邮箱 {Email} 发送密码重置邮件", request.Email);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "向邮箱 {Email} 发送密码重置邮件时发生错误", request.Email);
            return ServiceResult.Failure("发送重置邮件失败，请稍后重试");
        }
    }

    public async Task<ServiceResult> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            var user = await ValidatePasswordResetTokenAsync(request.Token);
            if (user == null) return ServiceResult.Failure("重置链接无效或已过期");

            if (user.Email?.ToLower() != request.Email.ToLower()) return ServiceResult.Failure("重置链接无效");

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            await ClearPasswordResetTokenAsync(user.Id);
            await RevokeAllUserSessionsAsync(user.Id);

            _logger.LogInformation("用户 {UserId} 密码重置成功", user.Id);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置邮箱 {Email} 密码时发生错误", request.Email);
            return ServiceResult.Failure("密码重置失败，请稍后重试");
        }
    }


    public async Task<UserSession> CreateSessionAsync(Guid userId, string? ipAddress, string? userAgent)
    {
        var sessionToken = GenerateSecureToken();
        var expiresAt = DateTime.UtcNow.AddDays(30); // 记住登录30天

        var session = new UserSession
        {
            Id = Guid.NewGuid(),
            UserId = userId,
            SessionToken = sessionToken,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            LastAccessedAt = DateTime.UtcNow,
            ExpiresAt = expiresAt
        };

        _dbContext.UserSessions.Add(session);
        await _dbContext.SaveChangesAsync();

        _logger.LogInformation("已为用户 {UserId} 创建会话: {SessionId}", userId, session.Id);
        return session;
    }

    public async Task<UserSession?> ValidateSessionAsync(string sessionToken)
    {
        var session = await _dbContext.UserSessions.Include(s => s.User)
            .Where(s => s.SessionToken == sessionToken && s.IsActive && s.ExpiresAt > DateTime.UtcNow && s.User.Status == UserAccountStatus.Active)
            .FirstOrDefaultAsync();

        if (session != null)
        {
            session.LastAccessedAt = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();
        }

        return session;
    }

    public async Task<bool> RevokeSessionAsync(string sessionToken)
    {
        var session = await _dbContext.UserSessions.Where(s => s.SessionToken == sessionToken && s.IsActive).FirstOrDefaultAsync();
        if (session == null) return false;
        session.IsActive = false;
        await _dbContext.SaveChangesAsync();

        _logger.LogInformation("已撤销会话: {SessionId}", session.Id);
        return true;
    }

    public async Task<int> RevokeAllUserSessionsAsync(Guid userId)
    {
        var count = await _dbContext.UserSessions.Where(s => s.UserId == userId && s.IsActive).ExecuteUpdateAsync(s => s.SetProperty(x => x.IsActive, false));

        _logger.LogInformation("已为用户 {UserId} 撤销 {Count} 个会话", userId, count);
        return count;
    }

    public async Task<int> CleanupExpiredSessionsAsync()
    {
        var count = await _dbContext.UserSessions.Where(s => s.ExpiresAt <= DateTime.UtcNow || !s.IsActive).ExecuteDeleteAsync();
        if (count > 0) _logger.LogInformation("已清理 {Count} 个过期会话", count);
        return count;
    }

    public async Task<List<UserSession>> GetUserActiveSessionsAsync(Guid userId)
    {
        return await _dbContext.UserSessions.Where(s => s.UserId == userId && s.IsActive && s.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(s => s.LastAccessedAt).ToListAsync();
    }

    public async Task<string> CreateEmailVerificationTokenAsync(Guid userId)
    {
        var token = GenerateSecureToken();
        var expiresAt = DateTime.UtcNow.AddHours(24); // 24小时有效

        await _dbContext.Users.Where(u => u.Id == userId).ExecuteUpdateAsync(u =>
            u.SetProperty(x => x.EmailVerificationToken, token).SetProperty(x => x.EmailVerificationTokenExpiresAt, expiresAt)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow));

        _logger.LogInformation("已为用户 {UserId} 创建邮箱验证令牌", userId);
        return token;
    }

    public async Task<User?> ValidateEmailVerificationTokenAsync(string email, string token)
    {
        var user = await _dbContext.Users.Where(u =>
            u.Email == email.ToLower() && u.EmailVerificationToken == token && u.EmailVerificationTokenExpiresAt > DateTime.UtcNow &&
            u.Status == UserAccountStatus.Active).FirstOrDefaultAsync();
        return user;
    }

    public async Task ClearEmailVerificationTokenAsync(Guid userId)
    {
        await _dbContext.Users.Where(u => u.Id == userId).ExecuteUpdateAsync(u =>
            u.SetProperty(x => x.EmailVerificationToken, (string?)null).SetProperty(x => x.EmailVerificationTokenExpiresAt, (DateTime?)null)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow));
    }

    public async Task<int> CleanupExpiredEmailVerificationTokensAsync()
    {
        var now = DateTime.UtcNow;
        var count = await _dbContext.Users.Where(u => u.EmailVerificationTokenExpiresAt <= now && u.EmailVerificationToken != null).ExecuteUpdateAsync(u =>
            u.SetProperty(x => x.EmailVerificationToken, (string?)null).SetProperty(x => x.EmailVerificationTokenExpiresAt, (DateTime?)null)
                .SetProperty(x => x.UpdatedAt, now));
        if (count > 0) _logger.LogInformation("已清理 {Count} 个过期邮箱验证令牌", count);
        return count;
    }

    public async Task<string> CreatePasswordResetTokenAsync(Guid userId)
    {
        var token = GenerateSecureToken();
        var expiresAt = DateTime.UtcNow.AddHours(1);

        await _dbContext.Users.Where(u => u.Id == userId).ExecuteUpdateAsync(u =>
            u.SetProperty(x => x.PasswordResetToken, token).SetProperty(x => x.PasswordResetTokenExpiresAt, expiresAt)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow));

        _logger.LogInformation("已为用户 {UserId} 创建密码重置令牌", userId);
        return token;
    }

    public async Task<User?> ValidatePasswordResetTokenAsync(string token)
    {
        var user = await _dbContext.Users
            .Where(u => u.PasswordResetToken == token && u.PasswordResetTokenExpiresAt > DateTime.UtcNow && u.Status == UserAccountStatus.Active)
            .FirstOrDefaultAsync();
        return user;
    }

    public async Task ClearPasswordResetTokenAsync(Guid userId)
    {
        await _dbContext.Users.Where(u => u.Id == userId).ExecuteUpdateAsync(u =>
            u.SetProperty(x => x.PasswordResetToken, (string?)null).SetProperty(x => x.PasswordResetTokenExpiresAt, (DateTime?)null)
                .SetProperty(x => x.UpdatedAt, DateTime.UtcNow));
    }

    public async Task<int> CleanupExpiredPasswordResetTokensAsync()
    {
        var now = DateTime.UtcNow;
        var count = await _dbContext.Users.Where(u => u.PasswordResetTokenExpiresAt <= now && u.PasswordResetToken != null).ExecuteUpdateAsync(u =>
            u.SetProperty(x => x.PasswordResetToken, (string?)null).SetProperty(x => x.PasswordResetTokenExpiresAt, (DateTime?)null)
                .SetProperty(x => x.UpdatedAt, now));
        if (count > 0) _logger.LogInformation("已清理 {Count} 个过期密码重置令牌", count);
        return count;
    }

    public async Task<int> CleanupInactiveAnonymousUsersAsync(int daysInactive = 180)
    {
        var inactiveThreshold = DateTime.UtcNow.AddDays(-daysInactive);
        var count = await _dbContext.Users.Where(u => u.UserType == UserType.Anonymous && u.LastActiveAt < inactiveThreshold).ExecuteDeleteAsync();
        if (count > 0) _logger.LogInformation("已清理 {Count} 个不活跃匿名用户", count);
        return count;
    }

    private static string GenerateSecureToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    // ==================== 新增方法占位符 ====================

    public async Task<ServiceResult> UpdateUserAsync(Guid userId, UpdateUserRequest request)
    {
        // TODO: 实现用户信息更新逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<UserStatsResponse>> GetUserStatsAsync(Guid userId)
    {
        // TODO: 实现用户统计信息获取逻辑
        await Task.Delay(1);
        var stats = new UserStatsResponse(0, 0, 0, 0, DateTime.UtcNow);
        return ServiceResult<UserStatsResponse>.Success(stats);
    }

    public async Task<ServiceResult> ResendEmailVerificationAsync(Guid userId)
    {
        // TODO: 实现重新发送邮箱验证邮件逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> DeleteAccountAsync(Guid userId)
    {
        // TODO: 实现账户删除逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<UserPreferencesResponse>> GetUserPreferencesAsync(Guid userId)
    {
        // TODO: 实现获取用户偏好设置逻辑
        await Task.Delay(1);
        var preferences = new UserPreferencesResponse("zh-CN", "auto", true, true, "1080p", "mp3", DateTime.UtcNow);
        return ServiceResult<UserPreferencesResponse>.Success(preferences);
    }

    public async Task<ServiceResult> UpdateUserPreferencesAsync(Guid userId, UpdateUserPreferencesRequest request)
    {
        // TODO: 实现更新用户偏好设置逻辑
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<UserActivityResponse>> GetUserActivityAsync(Guid userId)
    {
        // TODO: 实现获取用户活动记录逻辑
        await Task.Delay(1);
        var activity = new UserActivityResponse(new List<UserLoginRecord>(), new List<UserOperationRecord>(), DateTime.UtcNow.AddDays(-30),
            DateTime.UtcNow.AddDays(-60));
        return ServiceResult<UserActivityResponse>.Success(activity);
    }

    private static UserResponse MapToUserResponse(User user)
    {
        return new UserResponse(user.Id, user.UserType, user.Email, user.PlanType, user.PlanExpiresAt, user.Status, user.CreatedAt, user.LastActiveAt);
    }
}