using Api.Data;
using Api.Endpoints;
using Api.Middleware;
using Api.Scheduled;
using Api.Services;
using FluentValidation;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Scalar.AspNetCore;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddDbContext<AppDbContext>(options => options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

builder.Services.AddValidatorsFromAssemblyContaining<Program>();

builder.Services.Configure<NodeAuthenticationOptions>(builder.Configuration.GetSection(NodeAuthenticationOptions.SectionName));

builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
builder.Services.AddProblemDetails();

// 现有服务
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<EmailService>();
builder.Services.AddScoped<CacheService>();
builder.Services.AddScoped<HealthCheckService>();
builder.Services.AddScoped<YouTubeService>();
builder.Services.AddScoped<ProxyService>();
builder.Services.AddScoped<WorkerService>();
builder.Services.AddScoped<TaskService>();
builder.Services.AddScoped<TaskQueryService>();
builder.Services.AddScoped<TaskPublishService>();
builder.Services.AddScoped<FileDownloadService>();
builder.Services.AddScoped<MonitoringDataService>();
builder.Services.AddScoped<WorkerNodeMonitoringService>();
builder.Services.AddScoped<MonitoringPerformanceService>();
builder.Services.AddScoped<MonitoringRealtimeService>();
builder.Services.AddScoped<YouTubeSearchService>();
builder.Services.AddScoped<YouTubeCacheService>();
builder.Services.AddScoped<WorkerNodeManagementService>();

// 管理员服务
builder.Services.AddScoped<AdminAuthService>();
builder.Services.AddScoped<AdminDashboardService>();
builder.Services.AddScoped<AdminUserService>();
builder.Services.AddScoped<AdminTaskService>();
builder.Services.AddScoped<AdminLogService>();

// 系统配置服务
builder.Services.AddScoped<SystemConfigService>();
builder.Services.AddScoped<SystemFeatureService>();
builder.Services.AddScoped<SystemLimitService>();
builder.Services.AddScoped<SystemMaintenanceService>();
builder.Services.AddScoped<SystemInfoService>();
builder.Services.AddScoped<SystemOperationService>();

// 代理管理服务
builder.Services.AddScoped<ProxyManagementService>();
builder.Services.AddScoped<ProxyHealthService>();
builder.Services.AddScoped<ProxyStatsService>();
builder.Services.AddScoped<ProxyPoolService>();

// 分析统计服务
builder.Services.AddScoped<AnalyticsService>();
builder.Services.AddScoped<UserAnalyticsService>();
builder.Services.AddScoped<TaskAnalyticsService>();

// 通知服务
builder.Services.AddScoped<NotificationService>();
builder.Services.AddScoped<NotificationSettingsService>();
builder.Services.AddScoped<AnnouncementService>();
builder.Services.AddScoped<AdminNotificationService>();
builder.Services.AddScoped<NotificationTemplateService>();

// 内容管理服务
builder.Services.AddScoped<ContentModerationService>();
builder.Services.AddScoped<DmcaService>();
builder.Services.AddScoped<ContentReportService>();
builder.Services.AddScoped<ContentAnalysisService>();
builder.Services.AddScoped<CopyrightService>();

// 分析统计服务（补充）
builder.Services.AddScoped<DownloadAnalyticsService>();
builder.Services.AddScoped<PerformanceAnalyticsService>();
builder.Services.AddScoped<TrendAnalyticsService>();
builder.Services.AddScoped<ReportService>();
builder.Services.AddScoped<RealTimeAnalyticsService>();

// 支付系统服务
builder.Services.AddScoped<BillingPlanService>();
builder.Services.AddScoped<SubscriptionService>();
builder.Services.AddScoped<PaymentService>();
builder.Services.AddScoped<InvoiceService>();
builder.Services.AddScoped<UsageService>();
builder.Services.AddScoped<CouponService>();
builder.Services.AddScoped<BillingSettingsService>();

builder.Services.AddHttpClient<WorkerService>();

builder.Services.AddMemoryCache();

builder.Services.AddMassTransit(x =>
{
    // 添加消费者
    x.AddConsumer<TaskResultConsumer>();
    // x.AddConsumer<TaskProgressConsumer>();

    x.UsingRabbitMq((context, cfg) =>
    {
        cfg.Host(builder.Configuration.GetConnectionString("RabbitMQ") ?? "amqp://guest:guest@localhost:5672/");

        // 配置消费者端点
        cfg.ReceiveEndpoint("task-result-queue", e =>
        {
            e.ConfigureConsumer<TaskResultConsumer>(context);
            e.Durable = true;
        });

        /*
        cfg.ReceiveEndpoint("task-progress-queue", e =>
        {
            e.ConfigureConsumer<TaskProgressConsumer>(context);
            e.Durable = true;
        });
        */

        cfg.ConfigureEndpoints(context);
    });
});

builder.Services.AddHostedService<CleanupService>();
builder.Services.AddHostedService<ProxyHealthCheckService>();
builder.Services.AddHostedService<WorkerNodeHealthCheckService>();
builder.Services.AddHostedService<WorkerNodeMonitoringService>();
builder.Services.AddHostedService<TaskSchedulerService>();

builder.Services.AddOpenApi();

if (builder.Environment.IsDevelopment())
    builder.Services.AddCors(options =>
    {
        options.AddDefaultPolicy(policy =>
        {
            policy.WithOrigins("http://localhost:3000", "https://localhost:3000").AllowAnyMethod().AllowAnyHeader().AllowCredentials();
        });
    });

var app = builder.Build();

/*
using (var scope = app.Services.CreateScope())
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();

    // 启动健康检查
    var startupHealthCheckService = scope.ServiceProvider.GetRequiredService<HealthCheckService>();
    var healthCheckResult = await startupHealthCheckService.PerformStartupHealthCheckAsync();

    if (!healthCheckResult.IsSuccess)
    {
        logger.LogCritical("启动健康检查失败: {Error}", healthCheckResult.ErrorMessage);
        throw new InvalidOperationException($"启动健康检查失败: {healthCheckResult.ErrorMessage}");
    }

    logger.LogInformation("应用启动验证完成，所有检查通过");
}
*/

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
    app.UseCors();
}

app.UseExceptionHandler();

app.UseMiddleware<NodeAuthenticationMiddleware>();
app.UseMiddleware<AuthenticationMiddleware>();

// 用户端点
app.MapAuthEndpoints();
app.MapYouTubeEndpoints();
app.MapTaskEndpoints();
app.MapFileEndpoints();

// 管理员端点
app.MapAdminAuthEndpoints();
app.MapAdminDashboardEndpoints();
app.MapAdminUserEndpoints();
app.MapAdminTaskEndpoints();
app.MapAdminWorkerEndpoints();
app.MapAdminProxyEndpoints();
app.MapAdminContentEndpoints();

// 其他端点
app.MapBillingEndpoints();

app.MapGet("/", () => Results.Ok(new
{
    status = "Backend is running",
    timestamp = DateTime.UtcNow
})).WithTags("Health");

app.Run();