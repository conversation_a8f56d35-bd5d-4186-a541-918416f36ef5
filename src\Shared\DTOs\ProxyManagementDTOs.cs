using Shared.Common;

namespace Shared.DTOs;

// ==================== 代理管理 ====================

public record ProxyListItemResponse(
    Guid Id,
    string Host,
    int Port,
    string Username,
    ProxyType ProxyType,
    ProxyStatus Status,
    ProxyHealthStatus HealthStatus,
    int UsageCount,
    DateTime? LastUsedAt,
    DateTime CreatedAt);

public record CreateProxyRequest(string Host, int Port, string Username, string Password, ProxyType ProxyType);

public record ProxyResponse(Guid Id, string Host, int Port, string Username, ProxyType ProxyType, ProxyStatus Status, DateTime CreatedAt);

public record ProxyDetailResponse(
    Guid Id,
    string Host,
    int Port,
    string Username,
    ProxyType ProxyType,
    ProxyStatus Status,
    ProxyHealthStatus HealthStatus,
    int UsageCount,
    DateTime? LastUsedAt,
    DateTime? LastHealthCheckAt,
    DateTime CreatedAt,
    DateTime UpdatedAt,
    ProxyStatsDetailResponse Stats);

public record ProxyStatsDetailResponse(int TotalRequests, int SuccessfulRequests, int FailedRequests, double SuccessRate, double AverageResponseTime);

public record UpdateProxyRequest(string? Host, int? Port, string? Username, string? Password, ProxyType? ProxyType);

// ==================== 代理健康检查 ====================

public record ProxyTestResponse(bool IsHealthy, double ResponseTime, string? ErrorMessage, DateTime TestedAt);

public record ProxyHealthCheckResponse(int TotalProxies, int HealthyProxies, int UnhealthyProxies, List<SingleProxyHealthResponse> Results);

public record SingleProxyHealthResponse(Guid ProxyId, bool IsHealthy, double ResponseTime, string? ErrorMessage, DateTime CheckedAt);

public record ProxyHealthSummaryResponse(
    int TotalProxies,
    int HealthyProxies,
    int UnhealthyProxies,
    int UnknownProxies,
    double OverallHealthRate,
    DateTime LastCheckAt);

// ==================== 代理统计 ====================

public record ProxyStatsResponse(
    int TotalProxies,
    int ActiveProxies,
    int TotalRequests,
    double AverageResponseTime,
    double OverallSuccessRate,
    List<ProxyUsageStatsResponse> TopUsedProxies);

public record ProxyUsageStatsResponse(Guid ProxyId, string Host, int Port, int UsageCount, double SuccessRate);

public record ProxyDetailStatsResponse(
    Guid ProxyId,
    int TotalRequests,
    int SuccessfulRequests,
    int FailedRequests,
    double SuccessRate,
    double AverageResponseTime,
    List<ProxyUsageHistoryResponse> UsageHistory);

public record ProxyUsageHistoryResponse(DateTime Date, int RequestCount, double SuccessRate, double AverageResponseTime);

// ==================== 批量操作 ====================

public record ImportProxiesRequest(List<CreateProxyRequest> Proxies);

public record ImportProxiesResponse(int TotalImported, int SuccessfulImports, int FailedImports, List<string> Errors);

public record ExportProxiesResponse(string FileName, string DownloadUrl, int ProxyCount);

public record BatchProxyOperationRequest(List<Guid> ProxyIds);

public record BatchOperationResponse(int TotalProcessed, int SuccessfulOperations, int FailedOperations, List<string> Errors);

// ==================== 代理池管理 ====================

public record ProxyPoolStatusResponse(
    int TotalProxies,
    int AvailableProxies,
    int BusyProxies,
    int UnhealthyProxies,
    double PoolEfficiency,
    DateTime LastOptimizedAt);