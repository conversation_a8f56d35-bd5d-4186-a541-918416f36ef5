using Microsoft.AspNetCore.Http.HttpResults;
using Shared.Common;

namespace Api.Extensions;

public static class ResultExtensions
{
    public static IResult ToHttpResult<T>(ServiceResult<T> result)
    {
        if (result.IsSuccess) return ApiOk(result.Data!);
        return GetHttpResultFromErrorCode(result.ErrorCode, result.ErrorMessage);
    }

    public static IResult ToHttpResult(ServiceResult result)
    {
        if (result.IsSuccess) return ApiOk();
        return GetHttpResultFromErrorCode(result.ErrorCode, result.ErrorMessage);
    }

    public static Ok<ApiResponse<T>> ApiOk<T>(T data, string? message = null)
    {
        return TypedResults.Ok(ApiResponse<T>.Ok(data, message));
    }

    public static Ok<ApiResponse> ApiOk(string? message = null)
    {
        return TypedResults.Ok(ApiResponse.Ok(message));
    }

    public static BadRequest<ApiResponse> ApiBadRequest(string message, string? errorCode = null)
    {
        return TypedResults.BadRequest(ApiResponse.Error(errorCode ?? ErrorCodes.INVALID_ARGUMENT, message));
    }

    public static BadRequest<ApiResponse> ApiValidationError(List<ValidationError> errors)
    {
        return TypedResults.BadRequest(ApiResponse.ValidationError(errors));
    }

    public static UnauthorizedHttpResult ApiUnauthorized()
    {
        return TypedResults.Unauthorized();
    }

    public static ForbidHttpResult ApiForbidden()
    {
        return TypedResults.Forbid();
    }

    public static NotFound<ApiResponse> ApiNotFound(string? message = null)
    {
        return TypedResults.NotFound(ApiResponse.Error(ErrorCodes.NOT_FOUND, message ?? ErrorCodes.GetDescription(ErrorCodes.NOT_FOUND)));
    }

    public static Conflict<ApiResponse> ApiConflict(string? message = null)
    {
        return TypedResults.Conflict(ApiResponse.Error(ErrorCodes.CONFLICT, message ?? ErrorCodes.GetDescription(ErrorCodes.CONFLICT)));
    }

    /// <summary>
    ///     处理ServiceResult，成功时返回指定消息，失败时转换为HTTP结果
    /// </summary>
    public static IResult ToHttpResultOrOk(ServiceResult result, string? successMessage = null)
    {
        return result.IsSuccess ? ApiOk(successMessage) : ToHttpResult(result);
    }

    /// <summary>
    ///     处理ServiceResult<T>，成功时返回指定消息，失败时转换为HTTP结果
    /// </summary>
    public static IResult ToHttpResultOrOk<T>(ServiceResult<T> result, string? successMessage = null)
    {
        return result.IsSuccess ? ApiOk(successMessage) : ToHttpResult(result);
    }

    private static IResult GetHttpResultFromErrorCode(string? errorCode, string? errorMessage)
    {
        return errorCode switch
        {
            ErrorCodes.NOT_FOUND or ErrorCodes.USER_NOT_FOUND or ErrorCodes.TASK_NOT_FOUND or ErrorCodes.BATCH_TASK_NOT_FOUND
                or ErrorCodes.YOUTUBE_VIDEO_NOT_FOUND or ErrorCodes.YOUTUBE_PLAYLIST_NOT_FOUND or ErrorCodes.YOUTUBE_CHANNEL_NOT_FOUND
                or ErrorCodes.WORKER_NOT_FOUND or ErrorCodes.FILE_NOT_FOUND or ErrorCodes.DIRECTORY_NOT_FOUND => ApiNotFound(errorMessage),
            ErrorCodes.UNAUTHORIZED or ErrorCodes.AUTH_TOKEN_EXPIRED or ErrorCodes.AUTH_TOKEN_INVALID or ErrorCodes.AUTH_CREDENTIALS_INVALID =>
                ApiUnauthorized(),
            ErrorCodes.FORBIDDEN or ErrorCodes.USER_DISABLED => ApiForbidden(),
            ErrorCodes.CONFLICT or ErrorCodes.USER_EMAIL_EXISTS or ErrorCodes.TASK_ALREADY_STARTED or ErrorCodes.TASK_ALREADY_COMPLETED =>
                ApiConflict(errorMessage),
            _ => ApiBadRequest(errorMessage!, errorCode)
        };
    }
}