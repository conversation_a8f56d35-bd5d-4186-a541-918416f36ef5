using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class SystemOperationService
{
    public async Task<ServiceResult> ClearCacheAsync()
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> RestartSystemAsync()
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<BackupResponse>> CreateBackupAsync()
    {
        await Task.Delay(1);
        return ServiceResult<BackupResponse>.Failure("功能暂未实现");
    }
}