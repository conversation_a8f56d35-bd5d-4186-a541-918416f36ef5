using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class ProxyPoolService
{
    public async Task<ServiceResult<ProxyPoolStatusResponse>> GetPoolStatusAsync()
    {
        await Task.Delay(1);
        return ServiceResult<ProxyPoolStatusResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> RefreshPoolAsync()
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> OptimizePoolAsync()
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }
}