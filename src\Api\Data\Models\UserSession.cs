using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Api.Data.Models;

public class UserSession
{
    public Guid Id { get; init; }
    public required Guid UserId { get; set; }
    public required string SessionToken { get; set; }
    public string? DeviceInfo { get; set; }
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; init; }
    public DateTime LastAccessedAt { get; set; }
    public DateTime ExpiresAt { get; set; }

    public User User { get; set; } = null!;
}

public class UserSessionConfiguration : IEntityTypeConfiguration<UserSession>
{
    public void Configure(EntityTypeBuilder<UserSession> builder)
    {
        builder.ToTable("user_sessions", "public");
        builder.HasKey(us => us.Id);
        builder.Property(us => us.Id).ValueGeneratedNever();
        builder.Property(us => us.UserId).IsRequired().HasColumnName("user_id");
        builder.Property(us => us.SessionToken).IsRequired().HasMaxLength(255).HasColumnName("session_token");
        builder.Property(us => us.DeviceInfo).HasMaxLength(500).HasColumnName("device_info");
        builder.Property(us => us.IpAddress).HasMaxLength(45).HasColumnName("ip_address");
        builder.Property(us => us.UserAgent).HasMaxLength(1000).HasColumnName("user_agent");
        builder.Property(us => us.IsActive).IsRequired().HasColumnName("is_active").HasDefaultValue(true);
        builder.Property(us => us.CreatedAt).IsRequired().HasColumnName("created_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(us => us.LastAccessedAt).IsRequired().HasColumnName("last_accessed_at").HasDefaultValueSql("CURRENT_TIMESTAMP");
        builder.Property(us => us.ExpiresAt).IsRequired().HasColumnName("expires_at");

        builder.HasIndex(us => us.UserId).HasDatabaseName("ix_user_sessions_user_id");
        builder.HasIndex(us => us.SessionToken).IsUnique().HasDatabaseName("ix_user_sessions_session_token");
        builder.HasIndex(us => us.IsActive).HasDatabaseName("ix_user_sessions_is_active");
        builder.HasIndex(us => us.ExpiresAt).HasDatabaseName("ix_user_sessions_expires_at");

        builder.HasOne(us => us.User).WithMany().HasForeignKey(us => us.UserId).OnDelete(DeleteBehavior.Cascade);
    }
}