using Shared.Common;
using Shared.DTOs;

namespace Api.Services;

public class AdminTaskService
{
    public async Task<ServiceResult<PagedResponse<AdminTaskListItemResponse>>> GetAllTasksAsync(int page, int pageSize, WorkerTaskStatus? status,
        WorkerTaskType? type, Guid? userId)
    {
        await Task.Delay(1);
        return ServiceResult<PagedResponse<AdminTaskListItemResponse>>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<AdminTaskDetailResponse>> GetTaskDetailAsync(Guid taskId)
    {
        await Task.Delay(1);
        return ServiceResult<AdminTaskDetailResponse>.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> ForceCancelTaskAsync(Guid taskId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult> RetryFailedTaskAsync(Guid taskId)
    {
        await Task.Delay(1);
        return ServiceResult.Failure("功能暂未实现");
    }

    public async Task<ServiceResult<TaskQueueStatusResponse>> GetTaskQueueStatusAsync()
    {
        await Task.Delay(1);
        return ServiceResult<TaskQueueStatusResponse>.Failure("功能暂未实现");
    }
}