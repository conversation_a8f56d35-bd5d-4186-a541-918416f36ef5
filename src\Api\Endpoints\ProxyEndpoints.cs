using Api.Extensions;
using Api.Services;
using Shared.DTOs;

namespace Api.Endpoints;

public static class ProxyEndpoints
{
    public static void MapProxyEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/proxies").WithTags("Proxy Management").WithValidation();

        // ==================== 代理列表管理端点 ====================

        group.MapGet("/", async Task<IResult> (ProxyManagementService proxyService,
            int page = 1, int pageSize = 20, string? status = null, string? healthStatus = null) =>
        {
            var result = await proxyService.GetProxyListAsync(page, pageSize, status, healthStatus);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理列表");

        group.MapPost("/", async Task<IResult> (CreateProxyRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.CreateProxyAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("添加代理");

        group.MapGet("/{proxyId}", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.GetProxyDetailAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理详情");

        group.MapPost("/{proxyId}/update", async Task<IResult> (Guid proxyId, UpdateProxyRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.UpdateProxyAsync(proxyId, request);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已更新");
        }).WithSummary("更新代理");

        group.MapPost("/{proxyId}/delete", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.DeleteProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已删除");
        }).WithSummary("删除代理");

        // ==================== 代理状态管理端点 ====================

        group.MapPost("/{proxyId}/enable", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.EnableProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已启用");
        }).WithSummary("启用代理");

        group.MapPost("/{proxyId}/disable", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.DisableProxyAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理已禁用");
        }).WithSummary("禁用代理");

        group.MapPost("/{proxyId}/test", async Task<IResult> (Guid proxyId, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.TestProxyAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("测试代理连接");

        // ==================== 代理健康检查端点 ====================

        group.MapPost("/health-check", async Task<IResult> (ProxyHealthService healthService) =>
        {
            var result = await healthService.CheckAllProxiesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("批量健康检查");

        group.MapPost("/{proxyId}/health-check", async Task<IResult> (Guid proxyId, ProxyHealthService healthService) =>
        {
            var result = await healthService.CheckProxyHealthAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("单个代理健康检查");

        group.MapGet("/health/summary", async Task<IResult> (ProxyHealthService healthService) =>
        {
            var result = await healthService.GetHealthSummaryAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理健康状态摘要");

        // ==================== 代理统计端点 ====================

        group.MapGet("/stats", async Task<IResult> (ProxyStatsService statsService) =>
        {
            var result = await statsService.GetProxyStatsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理使用统计");

        group.MapGet("/{proxyId}/stats", async Task<IResult> (Guid proxyId, ProxyStatsService statsService) =>
        {
            var result = await statsService.GetProxyDetailStatsAsync(proxyId);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取单个代理统计");

        group.MapPost("/stats/reset", async Task<IResult> (ProxyStatsService statsService) =>
        {
            var result = await statsService.ResetAllStatsAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "统计数据已重置");
        }).WithSummary("重置所有统计数据");

        group.MapPost("/{proxyId}/stats/reset", async Task<IResult> (Guid proxyId, ProxyStatsService statsService) =>
        {
            var result = await statsService.ResetProxyStatsAsync(proxyId);
            return ResultExtensions.ToHttpResultOrOk(result, "代理统计已重置");
        }).WithSummary("重置单个代理统计");

        // ==================== 批量操作端点 ====================

        group.MapPost("/import", async Task<IResult> (ImportProxiesRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.ImportProxiesAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("批量导入代理");

        group.MapGet("/export", async Task<IResult> (ProxyManagementService proxyService) =>
        {
            var result = await proxyService.ExportProxiesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("导出代理配置");

        group.MapPost("/batch/enable", async Task<IResult> (BatchProxyOperationRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.BatchEnableProxiesAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("批量启用代理");

        group.MapPost("/batch/disable", async Task<IResult> (BatchProxyOperationRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.BatchDisableProxiesAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("批量禁用代理");

        group.MapPost("/batch/delete", async Task<IResult> (BatchProxyOperationRequest request, ProxyManagementService proxyService) =>
        {
            var result = await proxyService.BatchDeleteProxiesAsync(request);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("批量删除代理");

        // ==================== 代理池管理端点 ====================

        group.MapGet("/pool/status", async Task<IResult> (ProxyPoolService poolService) =>
        {
            var result = await poolService.GetPoolStatusAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取代理池状态");

        group.MapPost("/pool/refresh", async Task<IResult> (ProxyPoolService poolService) =>
        {
            var result = await poolService.RefreshPoolAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "代理池已刷新");
        }).WithSummary("刷新代理池");

        group.MapPost("/pool/optimize", async Task<IResult> (ProxyPoolService poolService) =>
        {
            var result = await poolService.OptimizePoolAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "代理池已优化");
        }).WithSummary("优化代理池");
    }
}