using Api.Extensions;
using Api.Services;
using Shared.DTOs;

namespace Api.Endpoints;

public static class SystemEndpoints
{
    public static void MapSystemEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/system").WithTags("System").WithValidation();

        // ==================== 系统设置端点 ====================

        group.MapGet("/settings", async Task<IResult> (SystemConfigService configService) =>
        {
            var result = await configService.GetAllSettingsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统设置");

        group.MapPost("/settings", async Task<IResult> (UpdateSystemSettingsRequest request, SystemConfigService configService) =>
        {
            var result = await configService.UpdateSettingsAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "系统设置已更新");
        }).WithSummary("更新系统设置");

        group.MapGet("/settings/{key}", async Task<IResult> (string key, SystemConfigService configService) =>
        {
            var result = await configService.GetSettingAsync(key);
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取特定设置");

        group.MapPost("/settings/{key}", async Task<IResult> (string key, UpdateSettingRequest request, SystemConfigService configService) =>
        {
            var result = await configService.UpdateSettingAsync(key, request);
            return ResultExtensions.ToHttpResultOrOk(result, "设置已更新");
        }).WithSummary("更新特定设置");

        // ==================== 功能开关端点 ====================

        group.MapGet("/features", async Task<IResult> (SystemFeatureService featureService) =>
        {
            var result = await featureService.GetAllFeaturesAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取功能开关列表");

        group.MapPost("/features/{feature}/enable", async Task<IResult> (string feature, SystemFeatureService featureService) =>
        {
            var result = await featureService.EnableFeatureAsync(feature);
            return ResultExtensions.ToHttpResultOrOk(result, "功能已启用");
        }).WithSummary("启用功能");

        group.MapPost("/features/{feature}/disable", async Task<IResult> (string feature, SystemFeatureService featureService) =>
        {
            var result = await featureService.DisableFeatureAsync(feature);
            return ResultExtensions.ToHttpResultOrOk(result, "功能已禁用");
        }).WithSummary("禁用功能");

        group.MapPost("/features/{feature}/toggle", async Task<IResult> (string feature, SystemFeatureService featureService) =>
        {
            var result = await featureService.ToggleFeatureAsync(feature);
            return ResultExtensions.ToHttpResultOrOk(result, "功能状态已切换");
        }).WithSummary("切换功能开关");

        // ==================== 限流配置端点 ====================

        group.MapGet("/limits", async Task<IResult> (SystemLimitService limitService) =>
        {
            var result = await limitService.GetLimitConfigAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取限流配置");

        group.MapPost("/limits", async Task<IResult> (UpdateLimitConfigRequest request, SystemLimitService limitService) =>
        {
            var result = await limitService.UpdateLimitConfigAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "限流配置已更新");
        }).WithSummary("更新限流配置");

        group.MapGet("/limits/user", async Task<IResult> (SystemLimitService limitService) =>
        {
            var result = await limitService.GetUserLimitsAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取用户限制配置");

        group.MapPost("/limits/user", async Task<IResult> (UpdateUserLimitsRequest request, SystemLimitService limitService) =>
        {
            var result = await limitService.UpdateUserLimitsAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "用户限制已更新");
        }).WithSummary("更新用户限制配置");

        // ==================== 维护模式端点 ====================

        group.MapGet("/maintenance/status", async Task<IResult> (SystemMaintenanceService maintenanceService) =>
        {
            var result = await maintenanceService.GetMaintenanceStatusAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取维护模式状态");

        group.MapPost("/maintenance/enable", async Task<IResult> (EnableMaintenanceRequest request, SystemMaintenanceService maintenanceService) =>
        {
            var result = await maintenanceService.EnableMaintenanceModeAsync(request);
            return ResultExtensions.ToHttpResultOrOk(result, "维护模式已启用");
        }).WithSummary("启用维护模式");

        group.MapPost("/maintenance/disable", async Task<IResult> (SystemMaintenanceService maintenanceService) =>
        {
            var result = await maintenanceService.DisableMaintenanceModeAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "维护模式已禁用");
        }).WithSummary("禁用维护模式");

        // ==================== 系统信息端点 ====================

        group.MapGet("/info", async Task<IResult> (SystemInfoService infoService) =>
        {
            var result = await infoService.GetSystemInfoAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统信息");

        group.MapGet("/version", async Task<IResult> (SystemInfoService infoService) =>
        {
            var result = await infoService.GetVersionInfoAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取版本信息");

        group.MapGet("/health", async Task<IResult> (SystemInfoService infoService) =>
        {
            var result = await infoService.GetSystemHealthAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统健康状态");

        group.MapGet("/status", async Task<IResult> (SystemInfoService infoService) =>
        {
            var result = await infoService.GetSystemStatusAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("获取系统状态");

        // ==================== 系统操作端点 ====================

        group.MapPost("/cache/clear", async Task<IResult> (SystemOperationService operationService) =>
        {
            var result = await operationService.ClearCacheAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "缓存已清理");
        }).WithSummary("清理系统缓存");

        group.MapPost("/restart", async Task<IResult> (SystemOperationService operationService) =>
        {
            var result = await operationService.RestartSystemAsync();
            return ResultExtensions.ToHttpResultOrOk(result, "系统重启已启动");
        }).WithSummary("重启系统");

        group.MapPost("/backup", async Task<IResult> (SystemOperationService operationService) =>
        {
            var result = await operationService.CreateBackupAsync();
            return ResultExtensions.ToHttpResult(result);
        }).WithSummary("创建系统备份");
    }
}